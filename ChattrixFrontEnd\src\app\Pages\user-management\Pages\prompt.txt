I need you to redesign the user-list component to match the UI shown in the provided image, with specific modifications. Here are the detailed requirements:

**Elements to Remove:**
1. Remove the route breadcrumb text (ventures/Usermanagement) from the upper section
2. Remove the "hide", "customize", and "export" buttons
3. Remove any "2FA auth" or similar authentication-related filters and table columns

**Elements to Keep/Implement:**
1. Replace the removed buttons with the existing filter functionality from our current codebase
2. Maintain the existing "reset" button with the same color, placement, and layout
3. Keep the overall table structure and styling to match the image
4. Preserve all existing functionality that's already implemented in our codebase

**Design Requirements:**
- Follow the black and white minimalist design theme established in our previous work
- Use light grayish-white backgrounds (#f8f9fa) for main content areas
- Maintain Material UI functionality while updating the visual appearance
- Ensure the layout matches the image as closely as possible while incorporating our design preferences

Please first examine the current user-list component implementation and the existing filter functionality, then implement these changes while maintaining code quality and existing functionality.