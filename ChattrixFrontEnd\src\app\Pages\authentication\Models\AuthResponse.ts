// Base API response interface matching backend Response model
export interface ApiResponse<T = any> {
  isSuccess: boolean;
  message: string;
  token?: string;
  data?: T;
}

// Authentication specific response
export interface AuthResponse extends ApiResponse<AuthData> {
  token?: string;
}

// Authentication data structure
export interface AuthData {
  requiresOtp?: boolean;
  RequiresOtp?: boolean; // Backend inconsistency - keeping both for compatibility
  userId?: string;
  UserId?: string; // Backend inconsistency - keeping both for compatibility
}

// Login response type
export interface LoginResponse extends AuthResponse {}

// Register response type
export interface RegisterResponse extends ApiResponse<any> {}

// OTP verification response
export interface OtpVerificationResponse extends AuthResponse {}

// Password reset related responses
export interface ForgotPasswordResponse extends ApiResponse<any> {}
export interface ResetPasswordResponse extends ApiResponse<any> {}
export interface VerifyResetTokenResponse extends ApiResponse<any> {}
