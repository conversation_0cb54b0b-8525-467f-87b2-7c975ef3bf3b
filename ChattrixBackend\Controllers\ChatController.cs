using ChattrixBackend.Core.Entities.UserManagement.ResponseModel;
using ChattrixBackend.Services.ChatServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ChattrixBackend.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ChatController : ControllerBase {
        private readonly IChatService _chatService;

        public ChatController(IChatService chatService) {
            _chatService = chatService;
        }

        private string GetCurrentUserId() {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
        }

        // Message endpoints
        [HttpPost("conversations/{conversationId}/messages")]
        public async Task<ActionResult<Response>> SendMessage(string conversationId, [FromBody] SendMessageRequest request) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.SendMessageAsync(
                userId,
                conversationId,
                request.Content,
                request.Type ?? "text",
                request.ReplyToMessageId,
                request.ClientMessageId
            );

            return Ok(response);
        }

        [HttpGet("conversations/{conversationId}/messages")]
        public async Task<ActionResult<Response>> GetMessages(string conversationId, [FromQuery] int page = 1, [FromQuery] int pageSize = 50) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.GetMessagesAsync(conversationId, userId, page, pageSize);
            return Ok(response);
        }

        [HttpPut("messages/{messageId}")]
        public async Task<ActionResult<Response>> EditMessage(string messageId, [FromBody] EditMessageRequest request) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.EditMessageAsync(messageId, userId, request.Content);
            return Ok(response);
        }

        [HttpDelete("messages/{messageId}")]
        public async Task<ActionResult<Response>> DeleteMessage(string messageId) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.DeleteMessageAsync(messageId, userId);
            return Ok(response);
        }

        // Conversation endpoints
        [HttpPost("conversations/private")]
        public async Task<ActionResult<Response>> CreatePrivateConversation([FromBody] CreatePrivateConversationRequest request) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.CreatePrivateConversationAsync(userId, request.ParticipantId);
            return Ok(response);
        }

        [HttpPost("conversations/group")]
        public async Task<ActionResult<Response>> CreateGroupConversation([FromBody] CreateGroupConversationRequest request) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.CreateGroupConversationAsync(
                userId,
                request.Name,
                request.Description,
                request.ParticipantIds
            );
            return Ok(response);
        }

        [HttpGet("conversations")]
        public async Task<ActionResult<Response>> GetConversations([FromQuery] int page = 1, [FromQuery] int pageSize = 20) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.GetConversationsAsync(userId, page, pageSize);
            return Ok(response);
        }

        [HttpGet("conversations/{conversationId}")]
        public async Task<ActionResult<Response>> GetConversation(string conversationId) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.GetConversationAsync(conversationId, userId);
            return Ok(response);
        }

        [HttpPut("conversations/{conversationId}")]
        public async Task<ActionResult<Response>> UpdateConversation(string conversationId, [FromBody] UpdateConversationRequest request) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.UpdateConversationAsync(conversationId, userId, request.Name, request.Description);
            return Ok(response);
        }

        // Participant management endpoints
        [HttpPost("conversations/{conversationId}/participants")]
        public async Task<ActionResult<Response>> AddParticipant(string conversationId, [FromBody] AddParticipantRequest request) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.AddParticipantAsync(conversationId, userId, request.ParticipantId);
            return Ok(response);
        }

        [HttpDelete("conversations/{conversationId}/participants/{participantId}")]
        public async Task<ActionResult<Response>> RemoveParticipant(string conversationId, string participantId) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.RemoveParticipantAsync(conversationId, userId, participantId);
            return Ok(response);
        }

        [HttpPost("conversations/{conversationId}/leave")]
        public async Task<ActionResult<Response>> LeaveConversation(string conversationId) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.LeaveConversationAsync(conversationId, userId);
            return Ok(response);
        }

        [HttpGet("conversations/{conversationId}/participants")]
        public async Task<ActionResult<Response>> GetConversationParticipants(string conversationId) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.GetConversationParticipantsAsync(conversationId, userId);
            return Ok(response);
        }

        // Message status endpoints
        [HttpPost("messages/{messageId}/status")]
        public async Task<ActionResult<Response>> UpdateMessageStatus(string messageId, [FromBody] UpdateMessageStatusRequest request) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.UpdateMessageStatusAsync(messageId, userId, request.Status);
            return Ok(response);
        }

        [HttpPost("conversations/{conversationId}/messages/read")]
        public async Task<ActionResult<Response>> MarkMessagesAsRead(string conversationId, [FromBody] MarkMessagesAsReadRequest request) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.MarkMessagesAsReadAsync(conversationId, userId, request.MessageIds);
            return Ok(response);
        }

        [HttpGet("messages/unread/count")]
        public async Task<ActionResult<Response>> GetUnreadMessagesCount() {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.GetUnreadMessagesCountAsync(userId);
            return Ok(response);
        }

        // Search endpoints
        [HttpGet("search/messages")]
        public async Task<ActionResult<Response>> SearchMessages([FromQuery] string query, [FromQuery] string? conversationId = null, [FromQuery] int page = 1, [FromQuery] int pageSize = 20) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.SearchMessagesAsync(userId, query, conversationId, page, pageSize);
            return Ok(response);
        }

        [HttpGet("search/conversations")]
        public async Task<ActionResult<Response>> SearchConversations([FromQuery] string query, [FromQuery] int page = 1, [FromQuery] int pageSize = 20) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.SearchConversationsAsync(userId, query, page, pageSize);
            return Ok(response);
        }

        // File upload endpoint
        [HttpPost("conversations/{conversationId}/files")]
        public async Task<ActionResult<Response>> UploadFile(string conversationId, IFormFile file) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            if (file == null || file.Length == 0) {
                return BadRequest(new Response {
                    IsSuccess = false,
                    Message = "No file provided"
                });
            }

            using var stream = file.OpenReadStream();
            var response = await _chatService.UploadFileAsync(userId, conversationId, stream, file.FileName, file.ContentType);
            return Ok(response);
        }
    }

    // Request models
    public class SendMessageRequest {
        public string Content { get; set; } = string.Empty;
        public string? Type { get; set; }
        public string? ReplyToMessageId { get; set; }
        public string? ClientMessageId { get; set; }
    }

    public class EditMessageRequest {
        public string Content { get; set; } = string.Empty;
    }

    public class CreatePrivateConversationRequest {
        public string ParticipantId { get; set; } = string.Empty;
    }

    public class CreateGroupConversationRequest {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public List<string>? ParticipantIds { get; set; }
    }

    public class UpdateConversationRequest {
        public string? Name { get; set; }
        public string? Description { get; set; }
    }

    public class AddParticipantRequest {
        public string ParticipantId { get; set; } = string.Empty;
    }

    public class UpdateMessageStatusRequest {
        public string Status { get; set; } = string.Empty;
    }

    public class MarkMessagesAsReadRequest {
        public List<string> MessageIds { get; set; } = new List<string>();
    }
}
