using System.ComponentModel.DataAnnotations;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;

namespace ChattrixBackend.Core.Entities.ChatManagement.MessageModel {
    public class MessageStatus {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string MessageId { get; set; } = string.Empty;

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public DeliveryStatus Status { get; set; } = DeliveryStatus.Sent;

        [Required]
        public DateTime StatusAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Message Message { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }

    public enum DeliveryStatus {
        Sent = 1,
        Delivered = 2,
        Read = 3
    }
}
