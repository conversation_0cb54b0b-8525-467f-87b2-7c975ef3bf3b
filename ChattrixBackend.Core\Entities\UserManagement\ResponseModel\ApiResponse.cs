﻿namespace ChattrixBackend.Core.Entities.UserManagement.ResponseModel {

    public class ApiResponse<T> {
        public bool IsSuccess { get; set; }


        public string? Message { get; set; }


        public T? Data { get; set; }


        public object? Errors { get; set; }


        public static ApiResponse<T> Success(T data, string message = "Operation completed successfully") {
            return new ApiResponse<T> {
                IsSuccess = true,
                Message = message,
                Data = data
            };
        }

        public static ApiResponse<T> Failure(string message, object? errors = null) {
            return new ApiResponse<T> {
                IsSuccess = false,
                Message = message,
                Errors = errors
            };
        }
    }
}
