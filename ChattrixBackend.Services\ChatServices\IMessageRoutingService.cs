using ChattrixBackend.Core.Entities.ChatManagement.MessageModel;
using ChattrixBackend.Core.Entities.ChatManagement.WebSocketModel;

namespace ChattrixBackend.Services.ChatServices {
    public interface IMessageRoutingService {
        Task RouteMessageAsync(Message message);
        Task RouteMessageStatusUpdateAsync(string messageId, string userId, DeliveryStatus status);
        Task RouteTypingIndicatorAsync(string conversationId, string userId, bool isTyping);
        Task RoutePresenceUpdateAsync(string userId, string status);
        Task RouteConversationUpdateAsync(string conversationId, object updateData);
        Task RouteParticipantUpdateAsync(string conversationId, string participantId, string action);
        Task NotifyMessageDeliveryAsync(string messageId, List<string> participantIds);
        Task NotifyMessageReadAsync(string messageId, string userId);
        Task BroadcastSystemMessageAsync(string conversationId, string systemMessage);
    }
}
