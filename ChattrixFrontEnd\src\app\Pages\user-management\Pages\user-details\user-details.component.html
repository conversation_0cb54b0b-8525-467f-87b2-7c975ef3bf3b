<div class="user-details-container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="header-content">
      <div class="header-navigation">
        <button mat-icon-button class="back-btn" (click)="onBackToList()" matTooltip="Back to User List">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="header-text">
          <h1 class="page-title">User Details</h1>
          <p class="page-subtitle">View and manage user information</p>
        </div>
      </div>
      <div class="header-actions" *ngIf="user">
        <button mat-raised-button color="primary" class="edit-btn" (click)="onEditUser()">
          <mat-icon>edit</mat-icon>
          Edit User
        </button>
      </div>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p class="loading-text">Loading user details...</p>
  </div>

  <!-- User Details Content -->
  <div *ngIf="!isLoading && user" class="details-content">
    <!-- User Profile Section -->
    <div class="profile-section">
      <mat-card class="profile-card">
        <div class="profile-header">
          <!-- Profile Picture -->
          <div class="profile-picture">
            <img
              *ngIf="user.profileImageUrl"
              [src]="user.profileImageUrl"
              [alt]="user.fullName"
              class="profile-image"
            />
            <div *ngIf="!user.profileImageUrl" class="profile-placeholder">
              <span class="initials">{{ getUserInitials(user) }}</span>
            </div>
          </div>

          <!-- Basic Info -->
          <div class="basic-info">
            <h2 class="user-name">{{ user.fullName || 'N/A' }}</h2>
            <p class="user-email">{{ user.email || 'N/A' }}</p>
            <div class="status-badge">
              <mat-chip [class]="user.isActive ? 'status-active' : 'status-inactive'">
                <mat-icon>{{ user.isActive ? 'check_circle' : 'cancel' }}</mat-icon>
                {{ user.isActive ? 'Active' : 'Inactive' }}
              </mat-chip>
            </div>
          </div>
        </div>
      </mat-card>
    </div>

    <!-- Details Grid -->
    <div class="details-grid">
      <!-- Contact Information -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>contact_phone</mat-icon>
            Contact Information
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="info-row">
            <span class="info-label">Email:</span>
            <span class="info-value">{{ user.email || 'N/A' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Phone:</span>
            <span class="info-value">{{ user.phoneNumber || 'N/A' }}</span>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Role Information -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>security</mat-icon>
            Role & Permissions
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="roles-container">
            <mat-chip-set>
              <mat-chip
                *ngFor="let role of user.roles"
                [class]="getRoleClass(role)"
              >
                {{ getRoleDisplayName(role) }}
              </mat-chip>
            </mat-chip-set>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Account Information -->
      <mat-card class="info-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>account_circle</mat-icon>
            Account Information
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="info-row">
            <span class="info-label">User ID:</span>
            <span class="info-value">{{ user.id || 'N/A' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Created On:</span>
            <span class="info-value">{{ formatDate(user.createdOn) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Status:</span>
            <span class="info-value">{{ user.isActive ? 'Active' : 'Inactive' }}</span>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Additional Information -->
      <mat-card class="info-card" *ngIf="user.description">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>description</mat-icon>
            Description
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p class="description-text">{{ user.description }}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !user" class="error-state">
    <mat-icon class="error-icon">error_outline</mat-icon>
    <h3>User Not Found</h3>
    <p>The requested user could not be found.</p>
    <button mat-raised-button color="primary" (click)="onBackToList()">
      Back to User List
    </button>
  </div>
</div>
