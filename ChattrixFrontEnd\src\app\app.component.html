<!-- Loading State -->
<div *ngIf="isLoading" class="loading-container">
  <div class="loading-content">
    <mat-icon class="loading-icon">hourglass_empty</mat-icon>
    <p class="loading-text">Loading...</p>
  </div>
</div>

<!-- Unauthenticated Layout (Authentication Module) -->
<div *ngIf="!isLoading && !isAuthenticated" class="auth-layout">
  <router-outlet></router-outlet>
</div>

<!-- Authenticated Layout (Dashboard with Sidebar) -->
<div *ngIf="!isLoading && isAuthenticated" class="main-layout">
  <!-- Sidebar Navigation -->
  <app-side-bar class="sidebar"></app-side-bar>

  <!-- Main Content Area -->
  <div class="main-content">
    <router-outlet></router-outlet>
  </div>
</div>
