import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

// Material UI Modules
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDividerModule } from '@angular/material/divider';
import { MatMenuModule } from '@angular/material/menu';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';

// Layout Components
import { SideBarComponent } from './Components/side-bar/side-bar.component';
import { LogoutConfirmationDialogComponent } from './Components/logout-confirmation-dialog/logout-confirmation-dialog.component';

@NgModule({
  declarations: [SideBarComponent, LogoutConfirmationDialogComponent],
  imports: [
    CommonModule,
    RouterModule,
    // Material UI Modules
    MatSidenavModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatSlideToggleModule,
    MatDividerModule,
    MatMenuModule,
    MatToolbarModule,
    MatTooltipModule,
    MatCardModule,
    MatDialogModule,
  ],
  exports: [SideBarComponent],
})
export class LayoutModule {}
