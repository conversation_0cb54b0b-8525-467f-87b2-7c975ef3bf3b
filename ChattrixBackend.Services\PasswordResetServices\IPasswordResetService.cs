using ChattrixBackend.Core.Entities.UserManagement.PasswordResetTokenModel;

namespace ChattrixBackend.Services.PasswordResetServices {
    public interface IPasswordResetService {
        Task<string> GenerateResetTokenAsync(string userId, string email);
        Task<bool> VerifyResetTokenAsync(string email, string token);
        Task<bool> IsTokenValidAsync(string email, string token);
        Task MarkTokenAsUsedAsync(string email, string token);
        Task<bool> CanRequestResetAsync(string email);
        Task CleanupExpiredTokensAsync();
        Task<PasswordResetToken?> GetValidTokenAsync(string email, string token);
    }
}
