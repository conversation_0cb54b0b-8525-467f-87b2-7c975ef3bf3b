<div class="auth-container">
  <mat-card class="auth-card profile-card">
    <!-- Header Section -->
    <div class="auth-header">
      <div class="auth-logo">
        <img
          src="logo/logo2.png"
          alt="Chattrix Logo"
          (error)="onImageError($event)"
          [style.display]="logoLoaded ? 'block' : 'none'"
        />
        <div
          class="logo-fallback"
          [style.display]="logoLoaded ? 'none' : 'flex'"
        >
          <span class="logo-text">C</span>
        </div>
      </div>
      <h1 class="auth-title">Complete Your Profile</h1>
      <p class="auth-subtitle">
        Add a profile picture and tell us about yourself
      </p>
    </div>

    <!-- Profile Form -->
    <form
      [formGroup]="profileForm"
      (ngSubmit)="onCompleteProfile()"
      class="auth-form"
    >
      <!-- Profile Picture Upload Section -->
      <div class="profile-picture-section">
        <div class="profile-picture-container">
          <div
            class="profile-picture-preview clickable"
            [class.has-image]="imagePreview"
            (click)="triggerFileInput($event)"
            (keydown.enter)="triggerFileInput($event)"
            (keydown.space)="triggerFileInput($event)"
            tabindex="0"
            role="button"
            [attr.aria-label]="
              imagePreview ? 'Change profile picture' : 'Upload profile picture'
            "
          >
            <img
              *ngIf="imagePreview"
              [src]="imagePreview"
              alt="Profile Preview"
              class="preview-image"
            />
            <div *ngIf="!imagePreview" class="placeholder-content">
              <mat-icon class="upload-icon">add_a_photo</mat-icon>
              <span class="upload-text">Add Photo</span>
            </div>
            <div *ngIf="imagePreview" class="overlay-content">
              <mat-icon class="change-icon">edit</mat-icon>
              <span class="change-text">Change</span>
            </div>
            <button
              *ngIf="imagePreview"
              type="button"
              mat-icon-button
              class="remove-image-btn"
              (click)="removeImage($event)"
              aria-label="Remove image"
            >
              <mat-icon>close</mat-icon>
            </button>
          </div>

          <input
            type="file"
            id="fileInput"
            accept="image/jpeg,image/jpg,image/png"
            (change)="onFileSelected($event)"
            style="display: none"
          />

          <p class="upload-hint">Supported formats: JPEG, JPG, PNG (Max 5MB)</p>
        </div>
      </div>

      <!-- Description Field -->
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>About You (Optional)</mat-label>
        <textarea
          matInput
          formControlName="description"
          placeholder="Tell us a bit about yourself"
          rows="4"
          maxlength="500"
        ></textarea>
        <mat-icon matSuffix>description</mat-icon>
        <mat-hint>{{ descriptionControl?.value?.length || 0 }}/500</mat-hint>
      </mat-form-field>

      <!-- Action Buttons -->
      <div class="button-group">
        <button
          type="button"
          mat-stroked-button
          class="back-button"
          (click)="onBack()"
          [disabled]="isLoading"
        >
          <mat-icon>arrow_back</mat-icon>
          Back
        </button>

        <button
          mat-raised-button
          color="primary"
          type="submit"
          class="complete-button"
          [disabled]="isLoading"
        >
          <div class="button-content">
            <mat-spinner
              *ngIf="isLoading"
              diameter="20"
              class="button-spinner"
            ></mat-spinner>
            <span [class.hidden]="isLoading">Complete Registration</span>
          </div>
        </button>
      </div>
    </form>

    <!-- Footer Section -->
    <div class="auth-footer">
      <mat-divider></mat-divider>
      <p class="footer-text">
        By completing registration, you agree to our Terms of Service and
        Privacy Policy.
      </p>
    </div>
  </mat-card>
</div>
