import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { AuthStateService } from './Pages/authentication/Services/AuthState.service';
import { AuthenticationService } from './Pages/authentication/Services/Authentication.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  standalone: false,
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  title = 'ChattrixFrontEnd';
  isLoading = true;
  isAuthenticated = false;

  constructor(
    private authState: AuthStateService,
    private authService: AuthenticationService,
  ) {}

  ngOnInit(): void {
    // Initialize authentication service to check for existing token
    this.authService.initializeAuthState();

    // Subscribe to authentication state
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state) => {
        this.isLoading = state.isLoading;
        this.isAuthenticated = state.isAuthenticated;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
