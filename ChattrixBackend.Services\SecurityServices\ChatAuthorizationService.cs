using ChattrixBackend.EntityFramworkCore.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ChattrixBackend.Services.SecurityServices {
    public interface IChatAuthorizationService {
        Task<bool> CanAccessConversationAsync(string userId, string conversationId);
        Task<bool> CanSendMessageAsync(string userId, string conversationId);
        Task<bool> CanEditMessageAsync(string userId, string messageId);
        Task<bool> CanDeleteMessageAsync(string userId, string messageId);
        Task<bool> CanManageParticipantsAsync(string userId, string conversationId);
        Task<bool> CanViewMessageAsync(string userId, string messageId);
        Task<bool> IsConversationAdminAsync(string userId, string conversationId);
    }

    public class ChatAuthorizationService : IChatAuthorizationService {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ChatAuthorizationService> _logger;

        public ChatAuthorizationService(ApplicationDbContext context, ILogger<ChatAuthorizationService> logger) {
            _context = context;
            _logger = logger;
        }

        public async Task<bool> CanAccessConversationAsync(string userId, string conversationId) {
            try {
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                return participant != null;
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error checking conversation access for user {userId} and conversation {conversationId}");
                return false;
            }
        }

        public async Task<bool> CanSendMessageAsync(string userId, string conversationId) {
            try {
                // Check if user is an active participant
                var participant = await _context.ConversationParticipants
                    .Include(cp => cp.Conversation)
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                if (participant == null) return false;

                // Check if conversation is active
                if (!participant.Conversation.IsActive) return false;

                // Additional checks can be added here (e.g., muted users, banned users, etc.)
                return true;
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error checking send message permission for user {userId} and conversation {conversationId}");
                return false;
            }
        }

        public async Task<bool> CanEditMessageAsync(string userId, string messageId) {
            try {
                var message = await _context.Messages
                    .FirstOrDefaultAsync(m => m.Id == messageId && m.SenderId == userId && !m.IsDeleted);

                // Users can only edit their own messages within a certain time frame (e.g., 15 minutes)
                if (message == null) return false;

                var editTimeLimit = TimeSpan.FromMinutes(15);
                return DateTime.UtcNow - message.SentAt <= editTimeLimit;
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error checking edit message permission for user {userId} and message {messageId}");
                return false;
            }
        }

        public async Task<bool> CanDeleteMessageAsync(string userId, string messageId) {
            try {
                var message = await _context.Messages
                    .Include(m => m.Conversation)
                    .ThenInclude(c => c.Participants)
                    .FirstOrDefaultAsync(m => m.Id == messageId && !m.IsDeleted);

                if (message == null) return false;

                // Users can delete their own messages
                if (message.SenderId == userId) return true;

                // Conversation admins can delete any message
                var userParticipant = message.Conversation.Participants
                    .FirstOrDefault(p => p.UserId == userId && p.IsActive);

                return userParticipant != null && 
                       (userParticipant.Role == Core.Entities.ChatManagement.ConversationModel.ParticipantRole.Admin ||
                        userParticipant.Role == Core.Entities.ChatManagement.ConversationModel.ParticipantRole.Owner);
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error checking delete message permission for user {userId} and message {messageId}");
                return false;
            }
        }

        public async Task<bool> CanManageParticipantsAsync(string userId, string conversationId) {
            try {
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                return participant != null && 
                       (participant.Role == Core.Entities.ChatManagement.ConversationModel.ParticipantRole.Admin ||
                        participant.Role == Core.Entities.ChatManagement.ConversationModel.ParticipantRole.Owner);
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error checking participant management permission for user {userId} and conversation {conversationId}");
                return false;
            }
        }

        public async Task<bool> CanViewMessageAsync(string userId, string messageId) {
            try {
                var message = await _context.Messages
                    .Include(m => m.Conversation)
                    .ThenInclude(c => c.Participants)
                    .FirstOrDefaultAsync(m => m.Id == messageId);

                if (message == null || message.IsDeleted) return false;

                // Check if user is a participant in the conversation
                return message.Conversation.Participants
                    .Any(p => p.UserId == userId && p.IsActive);
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error checking view message permission for user {userId} and message {messageId}");
                return false;
            }
        }

        public async Task<bool> IsConversationAdminAsync(string userId, string conversationId) {
            try {
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                return participant != null && 
                       (participant.Role == Core.Entities.ChatManagement.ConversationModel.ParticipantRole.Admin ||
                        participant.Role == Core.Entities.ChatManagement.ConversationModel.ParticipantRole.Owner);
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error checking admin status for user {userId} and conversation {conversationId}");
                return false;
            }
        }
    }
}
