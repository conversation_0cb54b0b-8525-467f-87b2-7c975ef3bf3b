import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, Observable, combineLatest } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';

import {
  UserProfileService,
  UserProfile,
} from '../../../Pages/chattrix/Services/UserProfile.service';
import {
  ThemeService,
  ThemeMode,
} from '../../../Pages/chattrix/Services/Theme.service';
import { AuthenticationService } from '../../../Pages/authentication/Services/Authentication.service';
import { LogoutConfirmationDialogComponent } from '../logout-confirmation-dialog/logout-confirmation-dialog.component';

interface NavigationItem {
  label: string;
  icon: string;
  route?: string;
  action?: () => void;
  requiresAdmin?: boolean;
  badge?: number;
}

@Component({
  selector: 'app-side-bar',
  standalone: false,
  templateUrl: './side-bar.component.html',
  styleUrl: './side-bar.component.scss',
})
export class SideBarComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // User profile data
  userProfile$: Observable<UserProfile | null>;
  hasAdminAccess$: Observable<boolean>;
  currentTheme$: Observable<ThemeMode>;
  userRoles: string[] = [];

  // Sidebar state
  isCollapsed = false;
  private readonly SIDEBAR_STATE_KEY = 'chattrix-sidebar-collapsed';

  // Navigation items
  navigationItems: NavigationItem[] = [
    {
      label: 'Messages',
      icon: 'message',
      route: '/dashboard',
      badge: 0, // Will be updated with actual message count
    },
    {
      label: 'User Management',
      icon: 'people',
      route: '/user-management',
      requiresAdmin: true,
    },
  ];

  constructor(
    private userProfileService: UserProfileService,
    private themeService: ThemeService,
    private authService: AuthenticationService,
    private dialog: MatDialog,
    public router: Router,
  ) {
    this.userProfile$ = this.userProfileService.userProfile$;
    this.hasAdminAccess$ = this.userProfileService.hasAdminAccess();
    this.currentTheme$ = this.themeService.currentTheme$;
  }

  ngOnInit(): void {
    // Initialize theme service to listen for system changes
    this.themeService.listenToSystemThemeChanges();

    // Load saved sidebar state
    this.loadSidebarState();

    // Subscribe to user roles for role-based navigation
    this.userProfileService
      .getUserRoles()
      .pipe(takeUntil(this.destroy$))
      .subscribe((roles) => {
        this.userRoles = roles;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Navigate to a specific route
   */
  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  /**
   * Get the current theme mode
   */
  get isDarkMode(): boolean {
    return this.themeService.isDarkMode;
  }

  /**
   * Toggle sidebar collapsed state
   */
  toggleSidebar(): void {
    this.isCollapsed = !this.isCollapsed;
    this.saveSidebarState();
  }

  /**
   * Load sidebar state from localStorage
   */
  private loadSidebarState(): void {
    try {
      const saved = localStorage.getItem(this.SIDEBAR_STATE_KEY);
      if (saved !== null) {
        this.isCollapsed = JSON.parse(saved);
      }
    } catch (error) {
      console.warn('Failed to load sidebar state:', error);
    }
  }

  /**
   * Save sidebar state to localStorage
   */
  private saveSidebarState(): void {
    try {
      localStorage.setItem(
        this.SIDEBAR_STATE_KEY,
        JSON.stringify(this.isCollapsed),
      );
    } catch (error) {
      console.warn('Failed to save sidebar state:', error);
    }
  }

  /**
   * Handle logout with confirmation
   */
  onLogout(): void {
    const dialogRef = this.dialog.open(LogoutConfirmationDialogComponent, {
      width: '400px',
      maxWidth: '95vw',
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((confirmed) => {
      if (confirmed) {
        this.themeService.removeTheme();
        this.authService.logout();
      }
    });
  }

  /**
   * Get user initials for avatar fallback
   */
  getUserInitials(profile: UserProfile | null): string {
    return profile?.initials || 'U';
  }

  /**
   * Get user display name
   */
  getUserDisplayName(profile: UserProfile | null): string {
    return profile?.displayName || 'User';
  }

  /**
   * Get user role display
   */
  getUserRole(profile: UserProfile | null): string {
    if (!profile) return 'User';

    if (Array.isArray(profile.role)) {
      return profile.role.join(', ');
    }
    return profile.role || 'User';
  }

  /**
   * Check if user should show admin role
   */
  shouldShowAdminRole(profile: UserProfile | null): boolean {
    if (!profile) return false;

    const roles = Array.isArray(profile.role) ? profile.role : [profile.role];
    return roles.some(
      (role) =>
        role &&
        (role.toLowerCase() === 'admin' ||
          role.toLowerCase() === 'super admin' ||
          role.toLowerCase() === 'superadmin'),
    );
  }

  /**
   * Check if navigation item should be visible
   * Simplified approach: hide User Management only for "user" role
   */
  shouldShowNavItem(item: NavigationItem, hasAdminAccess: boolean): boolean {
    if (item.requiresAdmin) {
      // Check if user has only "user" role (case-insensitive)
      const hasOnlyUserRole =
        this.userRoles.length === 1 &&
        this.userRoles[0].toLowerCase() === 'user';

      // Hide User Management for users with only "user" role
      if (hasOnlyUserRole) {
        return false;
      }

      // Show for admin and super admin roles
      const hasAdminRole = this.userRoles.some(
        (role) =>
          role.toLowerCase() === 'admin' ||
          role.toLowerCase() === 'super admin' ||
          role.toLowerCase() === 'superadmin',
      );

      return hasAdminRole;
    }
    return true;
  }

  /**
   * Handle navigation item click
   */
  onNavItemClick(item: NavigationItem): void {
    if (item.action) {
      item.action();
    } else if (item.route) {
      this.navigateTo(item.route);
    }
  }
}
