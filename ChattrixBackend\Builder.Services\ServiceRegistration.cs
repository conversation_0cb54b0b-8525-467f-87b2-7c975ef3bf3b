using ChattrixBackend.Services.AccountServices;
using ChattrixBackend.Services.BackgroundServices;
using ChattrixBackend.Services.ChatServices;
using ChattrixBackend.Services.OtpServices;
using ChattrixBackend.Services.PasswordResetServices;
using ChattrixBackend.Services.S3Services;
using ChattrixBackend.Services.SecurityServices;
using ChattrixBackend.Services.WebSocketServices;
using Identity.Services.Services;

namespace ChattrixBackend {
    public static class ServiceRegistration {
        public static IServiceCollection AddChattrixServices(this IServiceCollection services) {
            // Account & Auth services
            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<IOtpService, OtpService>();
            services.AddScoped<IPasswordResetService, PasswordResetService>();
            services.AddScoped<IAccountService, AccountService>();

            // Chat services
            services.AddScoped<IChatService, ChatService>();
            services.AddScoped<IAdvancedChatService, AdvancedChatService>();
            services.AddScoped<IMessageRoutingService, MessageRoutingService>();

            // WebSocket services
            services.AddSingleton<IWebSocketConnectionManager, WebSocketConnectionManager>();

            // Security services
            services.AddScoped<IChatAuthorizationService, ChatAuthorizationService>();
            services.AddSingleton<IRateLimitingService, RateLimitingService>();

            // Memory cache for rate limiting
            services.AddMemoryCache();

            // Background services
            services.AddHostedService<ChatCleanupService>();

            // S3 service (ensure IAmazonS3 is registered in Program.cs before this)
            services.AddScoped<IS3Service, OptimizedS3Service>();

            return services;
        }
    }
}