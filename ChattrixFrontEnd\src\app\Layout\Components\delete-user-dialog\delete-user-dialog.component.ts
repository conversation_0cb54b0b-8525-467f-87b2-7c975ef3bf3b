import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormControl, Validators } from '@angular/forms';
import { UserDetails } from '../../../Pages/user-management/Models/UserManagement';

export interface DeleteUserDialogData {
  user: UserDetails;
  currentUserRoles: string[];
}

@Component({
  selector: 'app-delete-user-dialog',
  standalone: false,
  templateUrl: './delete-user-dialog.component.html',
  styleUrl: './delete-user-dialog.component.scss',
})
export class DeleteUserDialogComponent {
  reasonControl = new FormControl('', [
    Validators.required,
    Validators.minLength(10),
  ]);
  isDeleting = false;

  constructor(
    public dialogRef: MatDialogRef<DeleteUserDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DeleteUserDialogData,
  ) {}

  get canDelete(): boolean {
    // Check if current user has permission to delete this user
    const isSuperAdmin = this.data.currentUserRoles.some(
      (role) =>
        role.toLowerCase() === 'super admin' ||
        role.toLowerCase() === 'superadmin',
    );

    if (isSuperAdmin) {
      return true; // Super admin can delete anyone
    }

    const isAdmin = this.data.currentUserRoles.some(
      (role) => role.toLowerCase() === 'admin',
    );

    if (isAdmin) {
      // Admin cannot delete super admins
      const userIsSuperAdmin = this.data.user.roles?.some(
        (role) =>
          role.toLowerCase() === 'super admin' ||
          role.toLowerCase() === 'superadmin',
      );
      return !userIsSuperAdmin;
    }

    return false; // Regular users cannot delete anyone
  }

  get warningMessage(): string {
    if (!this.canDelete) {
      return 'You do not have permission to delete this user.';
    }

    const userIsSuperAdmin = this.data.user.roles?.some(
      (role) =>
        role.toLowerCase() === 'super admin' ||
        role.toLowerCase() === 'superadmin',
    );

    if (userIsSuperAdmin) {
      return 'Warning: You are about to delete a Super Administrator. This action cannot be undone and may affect system administration.';
    }

    const userIsAdmin = this.data.user.roles?.some(
      (role) => role.toLowerCase() === 'admin',
    );

    if (userIsAdmin) {
      return 'Warning: You are about to delete an Administrator. This action cannot be undone and may affect user management capabilities.';
    }

    return 'This action cannot be undone. The user will be permanently removed from the system.';
  }

  get warningIcon(): string {
    if (!this.canDelete) {
      return 'block';
    }

    const userIsSuperAdmin = this.data.user.roles?.some(
      (role) =>
        role.toLowerCase() === 'super admin' ||
        role.toLowerCase() === 'superadmin',
    );

    if (userIsSuperAdmin) {
      return 'dangerous';
    }

    return 'warning';
  }

  get warningClass(): string {
    if (!this.canDelete) {
      return 'warning-blocked';
    }

    const userIsSuperAdmin = this.data.user.roles?.some(
      (role) =>
        role.toLowerCase() === 'super admin' ||
        role.toLowerCase() === 'superadmin',
    );

    if (userIsSuperAdmin) {
      return 'warning-danger';
    }

    return 'warning-caution';
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onConfirm(): void {
    if (!this.canDelete || !this.reasonControl.valid) {
      return;
    }

    const result = {
      confirmed: true,
      reason: this.reasonControl.value,
      user: this.data.user,
    };

    this.dialogRef.close(result);
  }

  getUserDisplayName(): string {
    return this.data.user.fullName || this.data.user.email || 'Unknown User';
  }

  getUserRoleDisplay(): string {
    if (Array.isArray(this.data.user.roles)) {
      return this.data.user.roles.join(', ');
    }
    return (this.data.user.roles as string) || 'User';
  }

  hasAdminPrivileges(): boolean {
    if (!this.data.user.roles) return false;
    return this.data.user.roles.some((role) =>
      role.toLowerCase().includes('admin'),
    );
  }
}
