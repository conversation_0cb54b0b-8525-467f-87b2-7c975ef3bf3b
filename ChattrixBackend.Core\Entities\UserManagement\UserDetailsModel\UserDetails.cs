﻿namespace ChattrixBackend.Core.Entities.UserManagement.UserDetailsModel {
    public class UserDetails {

        public string? Id { get; set; }
        public string? FullName { get; set; }
        public string? Email { get; set; }

        public bool IsActive { get; set; } = true;
        public string? ProfileImageUrl { get; set; }
        public List<string> Roles { get; set; }

        public string? PhoneNumber { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedOn { get; set; }



    }
}
