using System.ComponentModel.DataAnnotations;

namespace ChattrixBackend.Core.Entities.UserManagement.VerifyResetTokenModel {
    public class VerifyResetTokenRequest {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(6, MinimumLength = 6)]
        [RegularExpression(@"^[0-9]{6}$", ErrorMessage = "Reset token must be 6 digits")]
        public string ResetToken { get; set; } = string.Empty;
    }
}
