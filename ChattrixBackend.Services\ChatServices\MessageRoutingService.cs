using ChattrixBackend.Core.Entities.ChatManagement.MessageModel;
using ChattrixBackend.Core.Entities.ChatManagement.WebSocketModel;
using ChattrixBackend.EntityFramworkCore.Data;
using ChattrixBackend.Services.WebSocketServices;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace ChattrixBackend.Services.ChatServices {
    public class MessageRoutingService : IMessageRoutingService {
        private readonly IWebSocketConnectionManager _connectionManager;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<MessageRoutingService> _logger;

        public MessageRoutingService(
            IWebSocketConnectionManager connectionManager,
            IServiceProvider serviceProvider,
            ILogger<MessageRoutingService> logger) {
            _connectionManager = connectionManager;
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public async Task RouteMessageAsync(Message message) {
            try {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                // Get sender details
                var sender = await context.Users.FindAsync(message.SenderId);
                if (sender == null) return;

                // Get conversation participants
                var participants = await context.ConversationParticipants
                    .Where(cp => cp.ConversationId == message.ConversationId && cp.IsActive)
                    .Include(cp => cp.User)
                    .ToListAsync();

                var messageData = new MessageDeliveredResponse {
                    MessageId = message.Id,
                    ConversationId = message.ConversationId,
                    SenderId = message.SenderId,
                    SenderName = sender.FullName,
                    Content = message.Content,
                    Type = message.Type.ToString().ToLower(),
                    SentAt = message.SentAt,
                    ReplyToMessageId = message.ReplyToMessageId,
                    ClientMessageId = message.ClientMessageId
                };

                var wsMessage = new WebSocketMessage {
                    Type = "message_received",
                    Data = messageData
                };

                // Send to all participants except sender
                var recipientIds = participants
                    .Where(p => p.UserId != message.SenderId)
                    .Select(p => p.UserId)
                    .ToList();

                foreach (var recipientId in recipientIds) {
                    await _connectionManager.SendToUserAsync(recipientId, wsMessage);
                }

                // Update delivery status for online recipients
                await UpdateDeliveryStatusForOnlineUsers(message.Id, recipientIds);

                _logger.LogInformation("Message {MessageId} routed to {RecipientCount} recipients", message.Id, recipientIds.Count);
            } catch (Exception ex) {
                _logger.LogError(ex, "Error routing message {MessageId}", message.Id);
            }
        }

        public async Task RouteMessageStatusUpdateAsync(string messageId, string userId, DeliveryStatus status) {
            try {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                // Get message details
                var message = await context.Messages
                    .Include(m => m.Sender)
                    .FirstOrDefaultAsync(m => m.Id == messageId);

                if (message == null) return;

                // Get user details
                var user = await context.Users.FindAsync(userId);
                if (user == null) return;

                var statusUpdateData = new {
                    messageId = messageId,
                    userId = userId,
                    userName = user.FullName,
                    status = status.ToString().ToLower(),
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "message_status_updated",
                    Data = statusUpdateData
                };

                // Notify the message sender
                await _connectionManager.SendToUserAsync(message.SenderId, wsMessage);

                // For read receipts, also notify other conversation participants
                if (status == DeliveryStatus.Read) {
                    await _connectionManager.SendToConversationAsync(
                        message.ConversationId,
                        new WebSocketMessage {
                            Type = "message_read",
                            Data = new {
                                messageId = messageId,
                                readBy = userId,
                                readByName = user.FullName,
                                readAt = DateTime.UtcNow
                            }
                        },
                        userId // Exclude the user who read the message
                    );
                }

                _logger.LogInformation("Message status update routed: {MessageId} - {Status} by {UserId}", messageId, status, userId);
            } catch (Exception ex) {
                _logger.LogError(ex, "Error routing message status update for message {MessageId}", messageId);
            }
        }

        public async Task RouteTypingIndicatorAsync(string conversationId, string userId, bool isTyping) {
            try {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                var user = await context.Users.FindAsync(userId);
                if (user == null) return;

                var typingData = new {
                    conversationId = conversationId,
                    userId = userId,
                    userName = user.FullName,
                    isTyping = isTyping,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "user_typing",
                    Data = typingData
                };

                // Send to all conversation participants except the typing user
                await _connectionManager.SendToConversationAsync(conversationId, wsMessage, userId);

                _logger.LogDebug("Typing indicator routed: {UserId} - {IsTyping} in {ConversationId}", userId, isTyping, conversationId);
            } catch (Exception ex) {
                _logger.LogError(ex, "Error routing typing indicator for user {UserId} in conversation {ConversationId}", userId, conversationId);
            }
        }

        public async Task RoutePresenceUpdateAsync(string userId, string status) {
            try {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                var user = await context.Users.FindAsync(userId);
                if (user == null) return;

                var presenceData = new {
                    userId = userId,
                    userName = user.FullName,
                    status = status,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "user_presence_updated",
                    Data = presenceData
                };

                // Get all conversations where this user is a participant
                var conversationIds = await context.ConversationParticipants
                    .Where(cp => cp.UserId == userId && cp.IsActive)
                    .Select(cp => cp.ConversationId)
                    .ToListAsync();

                // Notify all participants in those conversations
                foreach (var conversationId in conversationIds) {
                    await _connectionManager.SendToConversationAsync(conversationId, wsMessage, userId);
                }

                _logger.LogInformation("Presence update routed: {UserId} - {Status}", userId, status);
            } catch (Exception ex) {
                _logger.LogError(ex, "Error routing presence update for user {UserId}", userId);
            }
        }

        public async Task RouteConversationUpdateAsync(string conversationId, object updateData) {
            try {
                var wsMessage = new WebSocketMessage {
                    Type = "conversation_updated",
                    Data = updateData
                };

                await _connectionManager.SendToConversationAsync(conversationId, wsMessage);

                _logger.LogInformation("Conversation update routed: {ConversationId}", conversationId);
            } catch (Exception ex) {
                _logger.LogError(ex, "Error routing conversation update for {ConversationId}", conversationId);
            }
        }

        public async Task RouteParticipantUpdateAsync(string conversationId, string participantId, string action) {
            try {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                var participant = await context.Users.FindAsync(participantId);
                if (participant == null) return;

                var participantData = new {
                    conversationId = conversationId,
                    participantId = participantId,
                    participantName = participant.FullName,
                    action = action,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "participant_updated",
                    Data = participantData
                };

                await _connectionManager.SendToConversationAsync(conversationId, wsMessage);

                _logger.LogInformation("Participant update routed: {ParticipantId} - {Action} in {ConversationId}", participantId, action, conversationId);
            } catch (Exception ex) {
                _logger.LogError(ex, "Error routing participant update for {ParticipantId} in conversation {ConversationId}", participantId, conversationId);
            }
        }

        public async Task NotifyMessageDeliveryAsync(string messageId, List<string> participantIds) {
            try {
                var deliveryData = new {
                    messageId = messageId,
                    deliveredTo = participantIds.Count,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "message_delivered",
                    Data = deliveryData
                };

                // Get message sender
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                var message = await context.Messages.FindAsync(messageId);
                if (message != null) {
                    await _connectionManager.SendToUserAsync(message.SenderId, wsMessage);
                }

                _logger.LogInformation("Message delivery notification sent for message {MessageId}", messageId);
            } catch (Exception ex) {
                _logger.LogError(ex, "Error notifying message delivery for message {MessageId}", messageId);
            }
        }

        public async Task NotifyMessageReadAsync(string messageId, string userId) {
            try {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                var message = await context.Messages
                    .Include(m => m.Sender)
                    .FirstOrDefaultAsync(m => m.Id == messageId);

                if (message == null) return;

                var user = await context.Users.FindAsync(userId);
                if (user == null) return;

                var readData = new {
                    messageId = messageId,
                    readBy = userId,
                    readByName = user.FullName,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "message_read_receipt",
                    Data = readData
                };

                // Notify the message sender
                await _connectionManager.SendToUserAsync(message.SenderId, wsMessage);

                _logger.LogInformation("Message read notification sent for message {MessageId} read by {UserId}", messageId, userId);
            } catch (Exception ex) {
                _logger.LogError(ex, "Error notifying message read for message {MessageId}", messageId);
            }
        }

        public async Task BroadcastSystemMessageAsync(string conversationId, string systemMessage) {
            try {
                var systemData = new {
                    conversationId = conversationId,
                    message = systemMessage,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "system_message",
                    Data = systemData
                };

                await _connectionManager.SendToConversationAsync(conversationId, wsMessage);

                _logger.LogInformation("System message broadcasted to conversation {ConversationId}", conversationId);
            } catch (Exception ex) {
                _logger.LogError(ex, "Error broadcasting system message to conversation {ConversationId}", conversationId);
            }
        }

        private async Task UpdateDeliveryStatusForOnlineUsers(string messageId, List<string> recipientIds) {
            try {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                var onlineRecipients = new List<string>();

                foreach (var recipientId in recipientIds) {
                    if (await _connectionManager.IsUserOnlineAsync(recipientId)) {
                        onlineRecipients.Add(recipientId);

                        // Update message status to delivered
                        var messageStatus = await context.MessageStatuses
                            .FirstOrDefaultAsync(ms => ms.MessageId == messageId && ms.UserId == recipientId);

                        if (messageStatus != null) {
                            messageStatus.Status = DeliveryStatus.Delivered;
                            messageStatus.StatusAt = DateTime.UtcNow;
                        }
                    }
                }

                if (onlineRecipients.Any()) {
                    await context.SaveChangesAsync();
                    await NotifyMessageDeliveryAsync(messageId, onlineRecipients);
                }
            } catch (Exception ex) {
                _logger.LogError(ex, "Error updating delivery status for message {MessageId}", messageId);
            }
        }
    }
}
