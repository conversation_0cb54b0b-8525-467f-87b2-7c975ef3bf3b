{"openapi": "3.0.1", "info": {"title": "ChattrixBackend", "version": "1.0"}, "paths": {"/api/Account/AddAdmin": {"post": {"tags": ["Account"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Email", "FullName"], "type": "object", "properties": {"Email": {"type": "string", "format": "email"}, "FullName": {"type": "string"}, "Password": {"type": "string"}, "IsActive": {"type": "boolean"}, "PhoneNumber": {"type": "string"}, "Description": {"type": "string"}, "ProfileImage": {"type": "string", "format": "binary"}, "ProfileImageUrl": {"type": "string"}, "Roles": {"type": "array", "items": {"type": "string"}}}}, "encoding": {"Email": {"style": "form"}, "FullName": {"style": "form"}, "Password": {"style": "form"}, "IsActive": {"style": "form"}, "PhoneNumber": {"style": "form"}, "Description": {"style": "form"}, "ProfileImage": {"style": "form"}, "ProfileImageUrl": {"style": "form"}, "Roles": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Account/Register": {"post": {"tags": ["Account"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Email", "FullName"], "type": "object", "properties": {"Email": {"type": "string", "format": "email"}, "FullName": {"type": "string"}, "Password": {"type": "string"}, "IsActive": {"type": "boolean"}, "PhoneNumber": {"type": "string"}, "Description": {"type": "string"}, "ProfileImage": {"type": "string", "format": "binary"}, "ProfileImageUrl": {"type": "string"}, "Roles": {"type": "array", "items": {"type": "string"}}}}, "encoding": {"Email": {"style": "form"}, "FullName": {"style": "form"}, "Password": {"style": "form"}, "IsActive": {"style": "form"}, "PhoneNumber": {"style": "form"}, "Description": {"style": "form"}, "ProfileImage": {"style": "form"}, "ProfileImageUrl": {"style": "form"}, "Roles": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Account/AddUser": {"post": {"tags": ["Account"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Email", "FullName"], "type": "object", "properties": {"Email": {"type": "string", "format": "email"}, "FullName": {"type": "string"}, "Password": {"type": "string"}, "IsActive": {"type": "boolean"}, "PhoneNumber": {"type": "string"}, "Description": {"type": "string"}, "ProfileImage": {"type": "string", "format": "binary"}, "ProfileImageUrl": {"type": "string"}, "Roles": {"type": "array", "items": {"type": "string"}}}}, "encoding": {"Email": {"style": "form"}, "FullName": {"style": "form"}, "Password": {"style": "form"}, "IsActive": {"style": "form"}, "PhoneNumber": {"style": "form"}, "Description": {"style": "form"}, "ProfileImage": {"style": "form"}, "ProfileImageUrl": {"style": "form"}, "Roles": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Account/UpdateUser/{userId}": {"put": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDetails"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserDetails"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/DeleteUser/{userId}": {"delete": {"tags": ["Account"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ToggleStatusRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ToggleStatusRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ToggleStatusRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/Login": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Login"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Login"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Login"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Account/VerifyOtp": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/ResendOtp": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResendOtpRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResendOtpRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResendOtpRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/GetUserById/{userId}": {"get": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/GetUsers": {"get": {"tags": ["Account"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortField", "in": "query", "schema": {"type": "string"}}, {"name": "SortOrder", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Role", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDetailsPagedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDetailsPagedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDetailsPagedResponseApiResponse"}}}}}}}, "/api/Account/GetAll": {"get": {"tags": ["Account"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDetailsListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDetailsListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDetailsListApiResponse"}}}}}}}, "/api/Account/ChangePassword": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/ForgotPassword": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/VerifyResetToken": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyResetTokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyResetTokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyResetTokenRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/ResetPassword": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chat/conversations/{conversationId}/messages": {"post": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "conversationId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendMessageRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendMessageRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendMessageRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}, "get": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "conversationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/messages/{messageId}": {"put": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "messageId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditMessageRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EditMessageRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EditMessageRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}, "delete": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "messageId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/conversations/private": {"post": {"tags": ["Cha<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePrivateConversationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePrivateConversationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePrivateConversationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/conversations/group": {"post": {"tags": ["Cha<PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGroupConversationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateGroupConversationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateGroupConversationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/conversations": {"get": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/conversations/{conversationId}": {"get": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "conversationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}, "put": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "conversationId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateConversationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateConversationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateConversationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/conversations/{conversationId}/participants": {"post": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "conversationId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddParticipantRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddParticipantRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddParticipantRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}, "get": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "conversationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/conversations/{conversationId}/participants/{participantId}": {"delete": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "conversationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "participantId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/conversations/{conversationId}/leave": {"post": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "conversationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/messages/{messageId}/status": {"post": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "messageId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMessageStatusRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMessageStatusRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMessageStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/conversations/{conversationId}/messages/read": {"post": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "conversationId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkMessagesAsReadRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MarkMessagesAsReadRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MarkMessagesAsReadRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/messages/unread/count": {"get": {"tags": ["Cha<PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/search/messages": {"get": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "query", "in": "query", "schema": {"type": "string"}}, {"name": "conversationId", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/search/conversations": {"get": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "query", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Chat/conversations/{conversationId}/files": {"post": {"tags": ["Cha<PERSON>"], "parameters": [{"name": "conversationId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Files/{folderName}/{fileName}": {"get": {"tags": ["Files"], "parameters": [{"name": "folderName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Presence/status": {"post": {"tags": ["Presence"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePresenceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePresenceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePresenceRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Presence/status/{userId}": {"get": {"tags": ["Presence"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Presence/online": {"get": {"tags": ["Presence"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/Roles": {"post": {"tags": ["Roles"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Roles"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Roles"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Roles"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Roles"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RoleResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RoleResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RoleResponse"}}}}}}}}, "/api/Roles/{id}": {"delete": {"tags": ["Roles"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Roles/assign": {"post": {"tags": ["Roles"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAssign"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleAssign"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleAssign"}}}}, "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AddParticipantRequest": {"type": "object", "properties": {"participantId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChangePasswordRequest": {"required": ["currentPassword", "newPassword", "userId"], "type": "object", "properties": {"userId": {"minLength": 1, "type": "string"}, "currentPassword": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 8, "pattern": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$", "type": "string"}}, "additionalProperties": false}, "CreateGroupConversationRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "participantIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CreatePrivateConversationRequest": {"type": "object", "properties": {"participantId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EditMessageRequest": {"type": "object", "properties": {"content": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ForgotPasswordRequest": {"required": ["email"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}}, "additionalProperties": false}, "Login": {"required": ["email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "MarkMessagesAsReadRequest": {"type": "object", "properties": {"messageIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ResendOtpRequest": {"required": ["userId"], "type": "object", "properties": {"userId": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "ResetPasswordRequest": {"required": ["confirmPassword", "email", "newPassword", "resetToken"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "resetToken": {"maxLength": 6, "minLength": 6, "pattern": "^[0-9]{6}$", "type": "string"}, "newPassword": {"minLength": 8, "pattern": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$", "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "Response": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}}, "additionalProperties": false}, "RoleAssign": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "roleId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RoleResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "totalUsers": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Roles": {"required": ["<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"roleName": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "SendMessageRequest": {"type": "object", "properties": {"content": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "replyToMessageId": {"type": "string", "nullable": true}, "clientMessageId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ToggleStatusRequest": {"type": "object", "properties": {"isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateConversationRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateMessageStatusRequest": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdatePresenceRequest": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserDetails": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "profileImageUrl": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UserDetailsListApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserDetails"}, "nullable": true}, "errors": {"nullable": true}}, "additionalProperties": false}, "UserDetailsPagedResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserDetails"}, "nullable": true}, "totalItems": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UserDetailsPagedResponseApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/UserDetailsPagedResponse"}, "errors": {"nullable": true}}, "additionalProperties": false}, "VerifyOtpRequest": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "otp": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VerifyResetTokenRequest": {"required": ["email", "resetToken"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "resetToken": {"maxLength": 6, "minLength": 6, "pattern": "^[0-9]{6}$", "type": "string"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}