using ChattrixBackend.Services.ChatServices;
using ChattrixBackend.Services.WebSocketServices;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace ChattrixBackend.Services.BackgroundServices {
    public class ChatCleanupService : BackgroundService {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ChatCleanupService> _logger;
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(5);

        public ChatCleanupService(IServiceProvider serviceProvider, ILogger<ChatCleanupService> logger) {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken) {
            _logger.LogInformation("Chat cleanup service started");

            while (!stoppingToken.IsCancellationRequested) {
                try {
                    await PerformCleanupTasks();
                    await Task.Delay(_cleanupInterval, stoppingToken);
                } catch (OperationCanceledException) {
                    // Expected when cancellation is requested
                    break;
                } catch (Exception ex) {
                    _logger.LogError(ex, "Error occurred during cleanup tasks");
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken); // Wait before retrying
                }
            }

            _logger.LogInformation("Chat cleanup service stopped");
        }

        private async Task PerformCleanupTasks() {
            using var scope = _serviceProvider.CreateScope();
            
            try {
                // Cleanup inactive WebSocket connections
                var connectionManager = scope.ServiceProvider.GetRequiredService<IWebSocketConnectionManager>();
                await connectionManager.CleanupInactiveConnectionsAsync();

                // Cleanup expired typing indicators
                var advancedChatService = scope.ServiceProvider.GetRequiredService<IAdvancedChatService>();
                await advancedChatService.CleanupExpiredTypingIndicatorsAsync();

                _logger.LogDebug("Cleanup tasks completed successfully");
            } catch (Exception ex) {
                _logger.LogError(ex, "Error during cleanup tasks execution");
            }
        }
    }
}
