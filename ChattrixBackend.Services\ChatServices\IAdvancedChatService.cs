using ChattrixBackend.Core.Entities.UserManagement.ResponseModel;

namespace ChattrixBackend.Services.ChatServices {
    public interface IAdvancedChatService {
        // Typing indicators
        Task<Response> StartTypingAsync(string userId, string conversationId);
        Task<Response> StopTypingAsync(string userId, string conversationId);
        Task<Response> GetTypingUsersAsync(string conversationId, string userId);
        Task CleanupExpiredTypingIndicatorsAsync();

        // Read receipts and message status
        Task<Response> GetMessageReadReceiptsAsync(string messageId, string userId);
        Task<Response> GetConversationReadStatusAsync(string conversationId, string userId);
        Task<Response> BulkUpdateMessageStatusAsync(string userId, List<string> messageIds, string status);

        // Enhanced presence
        Task<Response> SetDetailedPresenceAsync(string userId, string status, string? customMessage = null);
        Task<Response> GetUserDetailedPresenceAsync(string userId);
        Task<Response> GetConversationParticipantsPresenceAsync(string conversationId, string userId);
        Task<Response> GetRecentlyActiveUsersAsync(string userId, int hours = 24);

        // Message reactions
        Task<Response> AddMessageReactionAsync(string messageId, string userId, string reaction);
        Task<Response> RemoveMessageReactionAsync(string messageId, string userId, string reaction);
        Task<Response> GetMessageReactionsAsync(string messageId, string userId);

        // Message threading
        Task<Response> GetMessageThreadAsync(string messageId, string userId, int page = 1, int pageSize = 20);
        Task<Response> GetThreadSummaryAsync(string messageId, string userId);

        // Conversation insights
        Task<Response> GetConversationStatsAsync(string conversationId, string userId);
        Task<Response> GetUserChatStatsAsync(string userId, int days = 30);

        // Message search with advanced filters
        Task<Response> AdvancedMessageSearchAsync(string userId, string query, MessageSearchFilters filters);

        // Notification preferences
        Task<Response> UpdateNotificationPreferencesAsync(string userId, string conversationId, NotificationSettings settings);
        Task<Response> GetNotificationPreferencesAsync(string userId, string conversationId);

        // Message scheduling (future feature)
        Task<Response> ScheduleMessageAsync(string userId, string conversationId, string content, DateTime scheduledTime);
        Task<Response> GetScheduledMessagesAsync(string userId);
        Task<Response> CancelScheduledMessageAsync(string messageId, string userId);
    }

    public class MessageSearchFilters {
        public string? MessageType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SenderId { get; set; }
        public bool? HasAttachments { get; set; }
        public List<string>? ConversationIds { get; set; }
    }

    public class NotificationSettings {
        public bool IsMuted { get; set; }
        public DateTime? MutedUntil { get; set; }
        public bool ShowPreviews { get; set; }
        public bool SoundEnabled { get; set; }
        public string? CustomSound { get; set; }
    }
}
