﻿using ChattrixBackend.Core.Entities.UserManagement.PasswordResetTokenModel;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;
using ChattrixBackend.Core.Entities.UserManagement.UserOtpModel;
using ChattrixBackend.Core.Entities.ChatManagement.ConversationModel;
using ChattrixBackend.Core.Entities.ChatManagement.MessageModel;
using ChattrixBackend.Core.Entities.ChatManagement.PresenceModel;
using ChattrixBackend.Core.Entities.ChatManagement.TypingModel;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace ChattrixBackend.EntityFramworkCore.Data {
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser> {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
           : base(options) {
        }

        public DbSet<UserOtp> UserOtps { get; set; }
        public DbSet<PasswordResetToken> PasswordResetTokens { get; set; }

        // Chat-related DbSets
        public DbSet<Conversation> Conversations { get; set; }
        public DbSet<ConversationParticipant> ConversationParticipants { get; set; }
        public DbSet<Message> Messages { get; set; }
        public DbSet<MessageStatus> MessageStatuses { get; set; }
        public DbSet<UserPresence> UserPresences { get; set; }
        public DbSet<TypingIndicator> TypingIndicators { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder) {
            base.OnModelCreating(modelBuilder);

            // Configure chat entity relationships and constraints
            ConfigureChatEntities(modelBuilder);
        }

        private void ConfigureChatEntities(ModelBuilder modelBuilder) {
            // Conversation configuration
            modelBuilder.Entity<Conversation>(entity => {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.LastMessageAt);

                entity.HasOne(e => e.Creator)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedBy)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.LastMessage)
                    .WithMany()
                    .HasForeignKey(e => e.LastMessageId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // ConversationParticipant configuration
            modelBuilder.Entity<ConversationParticipant>(entity => {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.ConversationId, e.UserId }).IsUnique();
                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => e.IsActive);

                entity.HasOne(e => e.Conversation)
                    .WithMany(c => c.Participants)
                    .HasForeignKey(e => e.ConversationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany(u => u.ConversationParticipants)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.LastReadMessage)
                    .WithMany()
                    .HasForeignKey(e => e.LastReadMessageId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Message configuration
            modelBuilder.Entity<Message>(entity => {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.ConversationId);
                entity.HasIndex(e => e.SenderId);
                entity.HasIndex(e => e.SentAt);
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.IsDeleted);

                entity.HasOne(e => e.Conversation)
                    .WithMany(c => c.Messages)
                    .HasForeignKey(e => e.ConversationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Sender)
                    .WithMany(u => u.SentMessages)
                    .HasForeignKey(e => e.SenderId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ReplyToMessage)
                    .WithMany(m => m.Replies)
                    .HasForeignKey(e => e.ReplyToMessageId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // MessageStatus configuration
            modelBuilder.Entity<MessageStatus>(entity => {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.MessageId, e.UserId }).IsUnique();
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.StatusAt);

                entity.HasOne(e => e.Message)
                    .WithMany(m => m.MessageStatuses)
                    .HasForeignKey(e => e.MessageId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany(u => u.MessageStatuses)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // UserPresence configuration
            modelBuilder.Entity<UserPresence>(entity => {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.StatusAt);

                entity.HasOne(e => e.User)
                    .WithMany(u => u.PresenceHistory)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // TypingIndicator configuration
            modelBuilder.Entity<TypingIndicator>(entity => {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.ConversationId, e.UserId }).IsUnique();
                entity.HasIndex(e => e.IsActive);
                entity.HasIndex(e => e.LastUpdatedAt);

                entity.HasOne(e => e.Conversation)
                    .WithMany()
                    .HasForeignKey(e => e.ConversationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}
