<div class="auth-container">
  <div class="auth-card">
    <!-- Header Section -->
    <div class="auth-header">
      <h1 class="auth-title">Reset Password</h1>
      <p class="auth-subtitle">
        Create a new secure password for your account.
      </p>
    </div>

    <!-- Reset Password Form -->
    <form
      [formGroup]="resetPasswordForm"
      (ngSubmit)="onResetPassword()"
      class="auth-form"
    >
      <!-- New Password Field -->
      <div class="auth-input-container">
        <div class="auth-input-wrapper">
          <input
            [type]="hidePassword ? 'password' : 'text'"
            formControlName="newPassword"
            placeholder="Create a strong password"
            autocomplete="new-password"
            class="auth-input"
            [class.error]="
              newPasswordControl?.invalid && newPasswordControl?.touched
            "
            style="padding-right: 48px"
          />
          <mat-icon class="auth-input-icon">lock</mat-icon>
          <button
            type="button"
            (click)="togglePasswordVisibility()"
            class="auth-password-toggle"
            [attr.aria-label]="'Toggle password visibility'"
            [attr.aria-pressed]="!hidePassword"
          >
            <mat-icon>{{
              hidePassword ? "visibility_off" : "visibility"
            }}</mat-icon>
          </button>
        </div>
        <div
          *ngIf="
            newPasswordControl?.hasError('required') &&
            newPasswordControl?.touched
          "
          class="auth-error-message"
        >
          <mat-icon>error</mat-icon>
          Password is required
        </div>
        <div
          *ngIf="
            newPasswordControl?.hasError('minlength') &&
            newPasswordControl?.touched
          "
          class="auth-error-message"
        >
          <mat-icon>error</mat-icon>
          Password must be at least 8 characters long
        </div>
        <div
          *ngIf="
            newPasswordControl?.hasError('passwordStrength') &&
            newPasswordControl?.touched
          "
          class="auth-error-message"
        >
          <mat-icon>error</mat-icon>
          Password must contain uppercase, lowercase, number, and special
          character
        </div>
      </div>

      <!-- Confirm Password Field -->
      <div class="auth-input-container">
        <div class="auth-input-wrapper">
          <input
            [type]="hideConfirmPassword ? 'password' : 'text'"
            formControlName="confirmPassword"
            placeholder="Confirm your new password"
            autocomplete="new-password"
            class="auth-input"
            [class.error]="
              (confirmPasswordControl?.invalid ||
                resetPasswordForm.hasError('passwordMismatch')) &&
              confirmPasswordControl?.touched
            "
            style="padding-right: 48px"
          />
          <mat-icon class="auth-input-icon">lock</mat-icon>
          <button
            type="button"
            (click)="toggleConfirmPasswordVisibility()"
            class="auth-password-toggle"
            [attr.aria-label]="'Toggle password visibility'"
            [attr.aria-pressed]="!hideConfirmPassword"
          >
            <mat-icon>{{
              hideConfirmPassword ? "visibility_off" : "visibility"
            }}</mat-icon>
          </button>
        </div>
        <div
          *ngIf="
            confirmPasswordControl?.hasError('required') &&
            confirmPasswordControl?.touched
          "
          class="auth-error-message"
        >
          <mat-icon>error</mat-icon>
          Please confirm your password
        </div>
        <div
          *ngIf="
            resetPasswordForm.hasError('passwordMismatch') &&
            confirmPasswordControl?.touched
          "
          class="auth-error-message"
        >
          <mat-icon>error</mat-icon>
          Passwords do not match
        </div>
      </div>

      <!-- Password Strength Indicator -->
      <div
        class="password-strength"
        *ngIf="
          newPasswordControl?.hasError('passwordStrength') &&
          newPasswordControl?.touched
        "
      >
        <h4>Password must contain:</h4>
        <div class="strength-requirement">
          <div
            class="requirement-icon"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasUpperCase
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasUpperCase
            "
          >
            ✓
          </div>
          <span
            class="requirement-text"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasUpperCase
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasUpperCase
            "
          >
            At least one uppercase letter
          </span>
        </div>
        <div class="strength-requirement">
          <div
            class="requirement-icon"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasLowerCase
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasLowerCase
            "
          >
            ✓
          </div>
          <span
            class="requirement-text"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasLowerCase
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasLowerCase
            "
          >
            At least one lowercase letter
          </span>
        </div>
        <div class="strength-requirement">
          <div
            class="requirement-icon"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasNumeric
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasNumeric
            "
          >
            ✓
          </div>
          <span
            class="requirement-text"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasNumeric
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasNumeric
            "
          >
            At least one number
          </span>
        </div>
        <div class="strength-requirement">
          <div
            class="requirement-icon"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasSpecialChar
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasSpecialChar
            "
          >
            ✓
          </div>
          <span
            class="requirement-text"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasSpecialChar
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasSpecialChar
            "
          >
            At least one special character
          </span>
        </div>
      </div>

      <!-- Reset Password Button -->
      <button
        mat-raised-button
        type="submit"
        class="auth-button full-width"
        [disabled]="resetPasswordForm.invalid || isLoading"
      >
        <div class="button-content">
          <mat-spinner
            *ngIf="isLoading"
            diameter="20"
            class="button-spinner"
          ></mat-spinner>
          <span [class.hidden]="isLoading">Reset Password</span>
        </div>
      </button>
    </form>

    <!-- Footer Section -->
    <div class="auth-footer">
      <div class="back-to-login">
        <button mat-button (click)="onBackToLogin()" class="back-button">
          <mat-icon>arrow_back</mat-icon>
          Back to Login
        </button>
      </div>
    </div>
  </div>
</div>
