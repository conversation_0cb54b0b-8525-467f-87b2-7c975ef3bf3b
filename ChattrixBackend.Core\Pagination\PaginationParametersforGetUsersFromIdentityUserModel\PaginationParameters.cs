﻿namespace ChattrixBackend.Core.Pagination.PaginationParametersModel {
    public class PaginationParameters {

        private const int MaxPageSize = 50;
        private int _pageSize = 10;

        public int PageNumber { get; set; } = 1;

        public int PageSize {
            get => _pageSize;
            set => _pageSize = value > MaxPageSize ? MaxPageSize : value;
        }

        public string? SortField { get; set; }
        public string? SortOrder { get; set; }

        // Filter properties
        public string? Name { get; set; }
        public string? Role { get; set; }

        public string? Status { get; set; }
    }
}
