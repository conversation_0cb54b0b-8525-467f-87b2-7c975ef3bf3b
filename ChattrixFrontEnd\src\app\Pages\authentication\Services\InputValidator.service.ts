import { Injectable } from '@angular/core';
import { 
  LoginRequest, 
  RegisterRequest, 
  ForgotPasswordRequest, 
  ResetPasswordRequest,
  OtpVerificationRequest 
} from '../Models';

export interface ValidationResult {
  isValid: boolean;
  errors: { [key: string]: string[] };
}

@Injectable({
  providedIn: 'root'
})
export class InputValidatorService {

  /**
   * Validates login request data
   */
  validateLoginRequest(data: LoginRequest): ValidationResult {
    const errors: { [key: string]: string[] } = {};

    // Email validation
    if (!data.email) {
      errors['email'] = ['Email is required'];
    } else if (!this.isValidEmail(data.email)) {
      errors['email'] = ['Please enter a valid email address'];
    }

    // Password validation
    if (!data.password) {
      errors['password'] = ['Password is required'];
    } else if (data.password.length < 6) {
      errors['password'] = ['Password must be at least 6 characters long'];
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * Validates registration request data
   */
  validateRegisterRequest(data: RegisterRequest): ValidationResult {
    const errors: { [key: string]: string[] } = {};

    // Full name validation
    if (!data.fullName) {
      errors['fullName'] = ['Full name is required'];
    } else if (data.fullName.trim().length < 2) {
      errors['fullName'] = ['Full name must be at least 2 characters long'];
    } else if (data.fullName.length > 100) {
      errors['fullName'] = ['Full name must not exceed 100 characters'];
    }

    // Email validation
    if (!data.email) {
      errors['email'] = ['Email is required'];
    } else if (!this.isValidEmail(data.email)) {
      errors['email'] = ['Please enter a valid email address'];
    }

    // Password validation
    const passwordErrors = this.validatePassword(data.password);
    if (passwordErrors.length > 0) {
      errors['password'] = passwordErrors;
    }

    // Phone number validation (optional)
    if (data.phoneNumber && !this.isValidPhoneNumber(data.phoneNumber)) {
      errors['phoneNumber'] = ['Please enter a valid phone number'];
    }

    // Description validation (optional)
    if (data.description && data.description.length > 500) {
      errors['description'] = ['Description must not exceed 500 characters'];
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * Validates forgot password request
   */
  validateForgotPasswordRequest(data: ForgotPasswordRequest): ValidationResult {
    const errors: { [key: string]: string[] } = {};

    if (!data.email) {
      errors['email'] = ['Email is required'];
    } else if (!this.isValidEmail(data.email)) {
      errors['email'] = ['Please enter a valid email address'];
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * Validates reset password request
   */
  validateResetPasswordRequest(data: ResetPasswordRequest): ValidationResult {
    const errors: { [key: string]: string[] } = {};

    // Email validation
    if (!data.email) {
      errors['email'] = ['Email is required'];
    } else if (!this.isValidEmail(data.email)) {
      errors['email'] = ['Please enter a valid email address'];
    }

    // Reset token validation
    if (!data.resetToken) {
      errors['resetToken'] = ['Reset token is required'];
    } else if (data.resetToken.length !== 6) {
      errors['resetToken'] = ['Reset token must be 6 digits'];
    }

    // Password validation
    const passwordErrors = this.validatePassword(data.newPassword);
    if (passwordErrors.length > 0) {
      errors['newPassword'] = passwordErrors;
    }

    // Confirm password validation
    if (!data.confirmPassword) {
      errors['confirmPassword'] = ['Please confirm your password'];
    } else if (data.newPassword !== data.confirmPassword) {
      errors['confirmPassword'] = ['Passwords do not match'];
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * Validates OTP verification request
   */
  validateOtpRequest(data: OtpVerificationRequest): ValidationResult {
    const errors: { [key: string]: string[] } = {};

    if (!data.userId) {
      errors['userId'] = ['User ID is required'];
    }

    if (!data.otp) {
      errors['otp'] = ['OTP is required'];
    } else if (!/^\d{5}$/.test(data.otp)) {
      errors['otp'] = ['OTP must be 5 digits'];
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * Validates email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }

  /**
   * Validates password strength
   */
  private validatePassword(password: string): string[] {
    const errors: string[] = [];

    if (!password) {
      errors.push('Password is required');
      return errors;
    }

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (password.length > 128) {
      errors.push('Password must not exceed 128 characters');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return errors;
  }

  /**
   * Validates phone number format
   */
  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic phone number validation - adjust regex based on your requirements
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phoneNumber.replace(/[\s\-\(\)]/g, ''));
  }

  /**
   * Sanitizes string input to prevent XSS
   */
  sanitizeString(input: string): string {
    if (!input) return '';
    
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, ''); // Remove event handlers
  }

  /**
   * Sanitizes email input
   */
  sanitizeEmail(email: string): string {
    if (!email) return '';
    return email.trim().toLowerCase();
  }

  /**
   * Sanitizes phone number input
   */
  sanitizePhoneNumber(phoneNumber: string): string {
    if (!phoneNumber) return '';
    return phoneNumber.replace(/[^\d\+\-\(\)\s]/g, '');
  }
}
