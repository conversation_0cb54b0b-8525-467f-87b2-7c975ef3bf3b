{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend\\ChattrixBackend.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Core\\ChattrixBackend.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Core\\ChattrixBackend.Core.csproj", "projectName": "ChattrixBackend.Core", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Core\\ChattrixBackend.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[8.0.17, )"}, "MimeKit": {"target": "Package", "version": "[4.12.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.EntityFrameworkCore\\ChattrixBackend.EntityFrameworkCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.EntityFrameworkCore\\ChattrixBackend.EntityFrameworkCore.csproj", "projectName": "ChattrixBackend.EntityFrameworkCore", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.EntityFrameworkCore\\ChattrixBackend.EntityFrameworkCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.EntityFrameworkCore\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Core\\ChattrixBackend.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Core\\ChattrixBackend.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Services\\ChattrixBackend.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Services\\ChattrixBackend.Services.csproj", "projectName": "ChattrixBackend.Services", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Services\\ChattrixBackend.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Core\\ChattrixBackend.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Core\\ChattrixBackend.Core.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.EntityFrameworkCore\\ChattrixBackend.EntityFrameworkCore.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.EntityFrameworkCore\\ChattrixBackend.EntityFrameworkCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[4.0.2.1, )"}, "AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "MailKit": {"target": "Package", "version": "[4.12.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[8.0.17, )"}, "MimeKit": {"target": "Package", "version": "[4.12.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend\\ChattrixBackend.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend\\ChattrixBackend.csproj", "projectName": "ChattrixBackend", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend\\ChattrixBackend.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Core\\ChattrixBackend.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Core\\ChattrixBackend.Core.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.EntityFrameworkCore\\ChattrixBackend.EntityFrameworkCore.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.EntityFrameworkCore\\ChattrixBackend.EntityFrameworkCore.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Services\\ChattrixBackend.Services.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\NewChattrix\\ChattrixBackend.Services\\ChattrixBackend.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[4.0.2, )"}, "AWSSDK.S3": {"target": "Package", "version": "[4.0.2.1, )"}, "AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.17, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.17, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}