using ChattrixBackend.Core.Entities.UserManagement.ResponseModel;
using ChattrixBackend.Services.ChatServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ChattrixBackend.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PresenceController : ControllerBase {
        private readonly IChatService _chatService;

        public PresenceController(IChatService chatService) {
            _chatService = chatService;
        }

        private string GetCurrentUserId() {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
        }

        [HttpPost("status")]
        public async Task<ActionResult<Response>> UpdatePresence([FromBody] UpdatePresenceRequest request) {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.UpdateUserPresenceAsync(userId, request.Status);
            return Ok(response);
        }

        [HttpGet("status/{userId}")]
        public async Task<ActionResult<Response>> GetUserPresence(string userId) {
            var currentUserId = GetCurrentUserId();
            if (string.IsNullOrEmpty(currentUserId)) {
                return Unauthorized();
            }

            var response = await _chatService.GetUserPresenceAsync(userId);
            return Ok(response);
        }

        [HttpGet("online")]
        public async Task<ActionResult<Response>> GetOnlineUsers() {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId)) {
                return Unauthorized();
            }

            var response = await _chatService.GetOnlineUsersAsync();
            return Ok(response);
        }
    }

    public class UpdatePresenceRequest {
        public string Status { get; set; } = "online";
    }
}
