// node_modules/@angular/cdk/fesm2022/boolean-property-DaaVhX5A.mjs
function coerceBooleanProperty(value) {
  return value != null && `${value}` !== "false";
}

// node_modules/@angular/cdk/fesm2022/css-pixel-value-C_HEqLhI.mjs
function coerceCssPixelValue(value) {
  if (value == null) {
    return "";
  }
  return typeof value === "string" ? value : `${value}px`;
}

export {
  coerceBooleanProperty,
  coerceCssPixelValue
};
//# sourceMappingURL=chunk-IBYU652R.js.map
