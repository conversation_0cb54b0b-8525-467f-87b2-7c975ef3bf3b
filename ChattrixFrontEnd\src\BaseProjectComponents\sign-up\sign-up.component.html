<div class="auth-container">
  <mat-card class="auth-card signup-card">
    <!-- Header Section -->
    <div class="auth-header mt-4">
      <!-- <div class="auth-logo">
        <img
          src="logo/ChatLogo.jpg"
          alt="Chattrix Logo"
          (error)="onImageError($event)"
          [style.display]="logoLoaded ? 'block' : 'none'"
        />
        <div
          class="logo-fallback"
          [style.display]="logoLoaded ? 'none' : 'flex'"
        >
          <span class="logo-text">C</span>
        </div>
      </div> -->
      <h1 class="auth-title">Join <PERSON></h1>
      <p class="auth-subtitle">Create your account to get started</p>
    </div>

    <!-- Signup Form -->
    <form [formGroup]="signupForm" (ngSubmit)="onSignUp()" class="auth-form">
      <!-- Full Name Field -->
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Full Name</mat-label>
        <input
          matInput
          type="text"
          formControlName="fullName"
          placeholder="Enter your full name"
          autocomplete="name"
        />
        <mat-icon matSuffix>person</mat-icon>
        <mat-error *ngIf="fullNameControl?.hasError('required')">
          Full name is required
        </mat-error>
        <mat-error *ngIf="fullNameControl?.hasError('minlength')">
          Full name must be at least 2 characters long
        </mat-error>
      </mat-form-field>

      <!-- Email Field -->
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Email Address</mat-label>
        <input
          matInput
          type="email"
          formControlName="email"
          placeholder="Enter your email"
          autocomplete="email"
        />
        <mat-icon matSuffix>email</mat-icon>
        <mat-error *ngIf="emailControl?.hasError('required')">
          Email is required
        </mat-error>
        <mat-error *ngIf="emailControl?.hasError('email')">
          Please enter a valid email address
        </mat-error>
      </mat-form-field>

      <!-- Phone Number Field (Optional) -->
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Phone Number</mat-label>
        <input
          matInput
          type="tel"
          formControlName="phoneNumber"
          placeholder="Enter your phone number"
          autocomplete="tel"
        />
        <mat-icon matSuffix>phone</mat-icon>
        <mat-error *ngIf="phoneNumberControl?.hasError('pattern')">
          Please enter a valid phone number
        </mat-error>

        <mat-error *ngIf="phoneNumberControl?.hasError('minlength')">
          Phone number must be at least 10 digits long
        </mat-error>
      </mat-form-field>

      <!-- Password Field -->
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Password</mat-label>
        <input
          matInput
          [type]="hidePassword ? 'password' : 'text'"
          formControlName="password"
          placeholder="Create a strong password"
          autocomplete="new-password"
        />
        <button
          mat-icon-button
          matSuffix
          type="button"
          (click)="togglePasswordVisibility()"
          [attr.aria-label]="'Hide password'"
          [attr.aria-pressed]="hidePassword"
        >
          <mat-icon>{{
            hidePassword ? "visibility_off" : "visibility"
          }}</mat-icon>
        </button>
        <mat-error *ngIf="passwordControl?.hasError('required')">
          Password is required
        </mat-error>
        <mat-error *ngIf="passwordControl?.hasError('minlength')">
          Password must be at least 8 characters long
        </mat-error>
        <mat-error *ngIf="passwordControl?.hasError('passwordStrength')">
          Password must contain uppercase, lowercase, number, and special
          character
        </mat-error>
      </mat-form-field>

      <!-- Confirm Password Field -->
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Confirm Password</mat-label>
        <input
          matInput
          [type]="hideConfirmPassword ? 'password' : 'text'"
          formControlName="confirmPassword"
          placeholder="Confirm your password"
          autocomplete="new-password"
        />
        <button
          mat-icon-button
          matSuffix
          type="button"
          (click)="toggleConfirmPasswordVisibility()"
          [attr.aria-label]="'Hide password'"
          [attr.aria-pressed]="hideConfirmPassword"
        >
          <mat-icon>{{
            hideConfirmPassword ? "visibility_off" : "visibility"
          }}</mat-icon>
        </button>
        <mat-error *ngIf="confirmPasswordControl?.hasError('required')">
          Please confirm your password
        </mat-error>
        <mat-error
          *ngIf="
            signupForm.hasError('passwordMismatch') &&
            confirmPasswordControl?.touched
          "
        >
          Passwords do not match
        </mat-error>
      </mat-form-field>

      <!-- Signup Button -->
      <button
        mat-raised-button
        color="primary"
        type="submit"
        class="signup-button full-width"
        [disabled]="signupForm.invalid || isLoading"
      >
        <div class="button-content">
          <mat-spinner
            *ngIf="isLoading"
            diameter="20"
            class="button-spinner"
          ></mat-spinner>
          <span [class.hidden]="isLoading">Continue</span>
        </div>
      </button>
    </form>

    <!-- Footer Section -->
    <div class="auth-footer">
      <mat-divider></mat-divider>
      <p class="login-prompt">
        Already have an account?
        <button
          mat-button
          color="primary"
          (click)="onLogin()"
          class="login-link"
        >
          Sign In
        </button>
      </p>
    </div>
  </mat-card>
</div>
