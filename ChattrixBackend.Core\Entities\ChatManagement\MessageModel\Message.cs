using System.ComponentModel.DataAnnotations;
using ChattrixBackend.Core.Entities.ChatManagement.ConversationModel;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;

namespace ChattrixBackend.Core.Entities.ChatManagement.MessageModel {
    public class Message {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string ConversationId { get; set; } = string.Empty;

        [Required]
        public string SenderId { get; set; } = string.Empty;

        [Required]
        public MessageType Type { get; set; } = MessageType.Text;

        [Required]
        [StringLength(4000)]
        public string Content { get; set; } = string.Empty;

        // For file/media messages
        public string? FileUrl { get; set; }
        public string? FileName { get; set; }
        public long? FileSize { get; set; }
        public string? MimeType { get; set; }

        // For reply functionality
        public string? ReplyToMessageId { get; set; }

        [Required]
        public DateTime SentAt { get; set; } = DateTime.UtcNow;

        public DateTime? EditedAt { get; set; }

        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedAt { get; set; }

        // Message metadata
        public string? ClientMessageId { get; set; } // For client-side tracking
        public bool IsSystemMessage { get; set; } = false;

        // Navigation properties
        public virtual Conversation Conversation { get; set; } = null!;
        public virtual ApplicationUser Sender { get; set; } = null!;
        public virtual Message? ReplyToMessage { get; set; }
        public virtual ICollection<Message> Replies { get; set; } = new List<Message>();
        public virtual ICollection<MessageStatus> MessageStatuses { get; set; } = new List<MessageStatus>();
    }

    public enum MessageType {
        Text = 1,
        Image = 2,
        File = 3,
        Audio = 4,
        Video = 5,
        System = 6
    }
}
