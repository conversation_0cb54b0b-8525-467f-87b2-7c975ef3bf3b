<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="4.0.2" />
    <PackageReference Include="AWSSDK.S3" Version="4.0.2.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.17">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.17" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ChattrixBackend.Core\ChattrixBackend.Core.csproj" />
    <ProjectReference Include="..\ChattrixBackend.EntityFrameworkCore\ChattrixBackend.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\ChattrixBackend.Services\ChattrixBackend.Services.csproj" />
  </ItemGroup>

</Project>
