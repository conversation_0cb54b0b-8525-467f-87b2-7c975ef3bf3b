using System.Text.Json.Serialization;

namespace ChattrixBackend.Core.Entities.ChatManagement.WebSocketModel {
    public class WebSocketMessage {
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        [JsonPropertyName("data")]
        public object? Data { get; set; }

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [JsonPropertyName("messageId")]
        public string MessageId { get; set; } = Guid.NewGuid().ToString();
    }

    public class AuthenticationMessage {
        [JsonPropertyName("token")]
        public string Token { get; set; } = string.Empty;

        [JsonPropertyName("deviceInfo")]
        public string? DeviceInfo { get; set; }
    }

    public class ChatMessage {
        [JsonPropertyName("conversationId")]
        public string ConversationId { get; set; } = string.Empty;

        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        [JsonPropertyName("type")]
        public string Type { get; set; } = "text";

        [JsonPropertyName("replyToMessageId")]
        public string? ReplyToMessageId { get; set; }

        [JsonPropertyName("clientMessageId")]
        public string? ClientMessageId { get; set; }
    }

    public class TypingMessage {
        [JsonPropertyName("conversationId")]
        public string ConversationId { get; set; } = string.Empty;

        [JsonPropertyName("isTyping")]
        public bool IsTyping { get; set; }
    }

    public class PresenceMessage {
        [JsonPropertyName("status")]
        public string Status { get; set; } = "online";
    }

    public class MessageStatusUpdate {
        [JsonPropertyName("messageId")]
        public string MessageId { get; set; } = string.Empty;

        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;
    }

    // Response messages
    public class MessageDeliveredResponse {
        [JsonPropertyName("messageId")]
        public string MessageId { get; set; } = string.Empty;

        [JsonPropertyName("conversationId")]
        public string ConversationId { get; set; } = string.Empty;

        [JsonPropertyName("senderId")]
        public string SenderId { get; set; } = string.Empty;

        [JsonPropertyName("senderName")]
        public string SenderName { get; set; } = string.Empty;

        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        [JsonPropertyName("sentAt")]
        public DateTime SentAt { get; set; }

        [JsonPropertyName("replyToMessageId")]
        public string? ReplyToMessageId { get; set; }

        [JsonPropertyName("clientMessageId")]
        public string? ClientMessageId { get; set; }
    }

    public class ErrorResponse {
        [JsonPropertyName("error")]
        public string Error { get; set; } = string.Empty;

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;
    }
}
