using System.ComponentModel.DataAnnotations;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;

namespace ChattrixBackend.Core.Entities.ChatManagement.PresenceModel {
    public class UserPresence {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public PresenceStatus Status { get; set; } = PresenceStatus.Offline;

        [Required]
        public DateTime StatusAt { get; set; } = DateTime.UtcNow;

        public string? ConnectionId { get; set; }

        public string? DeviceInfo { get; set; }

        public string? IpAddress { get; set; }

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
    }

    public enum PresenceStatus {
        Online = 1,
        Away = 2,
        Busy = 3,
        Offline = 4
    }
}
