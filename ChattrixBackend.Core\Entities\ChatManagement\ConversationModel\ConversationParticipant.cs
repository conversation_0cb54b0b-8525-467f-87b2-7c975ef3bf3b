using ChattrixBackend.Core.Entities.ChatManagement.MessageModel;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;
using System.ComponentModel.DataAnnotations;

namespace ChattrixBackend.Core.Entities.ChatManagement.ConversationModel {
    public class ConversationParticipant {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string ConversationId { get; set; } = string.Empty;

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public ParticipantRole Role { get; set; } = ParticipantRole.Member;

        [Required]
        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LeftAt { get; set; }

        public bool IsActive { get; set; } = true;

        // Last message read tracking
        public string? LastReadMessageId { get; set; }
        public DateTime? LastReadAt { get; set; }

        // Notification settings
        public bool IsMuted { get; set; } = false;
        public DateTime? MutedUntil { get; set; }

        // Navigation properties
        public virtual Conversation Conversation { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
        public virtual Message? LastReadMessage { get; set; }
    }

    public enum ParticipantRole {
        Member = 1,
        Admin = 2,
        Owner = 3
    }
}
