﻿using ChattrixBackend.Utils.ResourceUtils;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace ChattrixBackend.Core.Entities.UserManagement.RegisterModel {
    public class AddUser {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
        [Required]

        public string FullName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;
        public string PhoneNumber { get; set; } = string.Empty;

        public string Description { get; set; } = string.Empty;

        // File upload
        [AllowedExtensions(new[] { ".jpg", ".png", ".pdf" })]
        [MaxFileSize(5 * 1024 * 1024)] // 5MB
        public IFormFile? ProfileImage { get; set; }

        // This will be set by the controller after saving the file
        public string? ProfileImageUrl { get; set; }




        public List<string> Roles { get; set; } = new List<string>();
    }
}
