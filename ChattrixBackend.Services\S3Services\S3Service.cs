﻿//﻿using Amazon.S3;
//using Amazon.S3.Model;
//using Amazon.S3.Transfer;
//using Microsoft.AspNetCore.Http;
//using Microsoft.Extensions.Configuration;
//using System;
//using System.IO;
//using System.Threading.Tasks;

//namespace Server.Services.S3Services
//{
//    public class S3Service : IS3Service
//    {
//        private readonly IAmazonS3 _s3Client;
//        private readonly string _bucketName = string.Empty;
//        private readonly IConfiguration _configuration;

//        public S3Service(IAmazonS3 s3Client, IConfiguration configuration)
//        {
//            _s3Client = s3Client;
//            _configuration = configuration;
//            _bucketName = _configuration["AWS:S3:BucketName"] ?? throw new ArgumentNullException(nameof(configuration), "AWS:S3:BucketName configuration is missing");
//        }

//        public async Task<string?> UploadFileAsync(IFormFile? file, string? folderName)
//        {
//            try
//            {
//                if (file == null || file.Length == 0)
//                    return null;

//                Console.WriteLine($"S3Service: Starting upload to bucket {_bucketName} in folder {folderName}");

//                // Generate a unique file name
//                var fileName = $"{Guid.NewGuid():N}_{Path.GetFileName(file.FileName)}";
//                Console.WriteLine($"S3Service: Generated filename: {fileName}");

//                // Create the full key (path) in S3
//                var key = string.IsNullOrEmpty(folderName)
//                    ? fileName
//                    : $"{folderName}/{fileName}";
//                Console.WriteLine($"S3Service: Full S3 key: {key}");

//                // Upload the file to S3
//                using var memoryStream = new MemoryStream();
//                await file.CopyToAsync(memoryStream);
//                memoryStream.Position = 0;
//                Console.WriteLine($"S3Service: File size: {memoryStream.Length} bytes");

//                var uploadRequest = new TransferUtilityUploadRequest
//                {
//                    InputStream = memoryStream,
//                    Key = key,
//                    BucketName = _bucketName,
//                    ContentType = file.ContentType
//                };

//                var fileTransferUtility = new TransferUtility(_s3Client);
//                Console.WriteLine($"S3Service: Starting S3 upload...");
//                await fileTransferUtility.UploadAsync(uploadRequest);
//                Console.WriteLine($"S3Service: Upload completed successfully");

//                // Return the key (path) of the file in S3
//                return key;
//            }
//            catch (Exception ex)
//            {
//                Console.WriteLine($"Error uploading file to S3: {ex.Message}");
//                Console.WriteLine($"Stack trace: {ex.StackTrace}");
//                if (ex.InnerException != null)
//                {
//                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
//                }
//                throw;
//            }
//        }

//        public async Task<bool> DeleteFileAsync(string? fileUrl)
//        {
//            try
//            {
//                if (string.IsNullOrEmpty(fileUrl))
//                    return false;

//                // Extract the key from the URL
//                var key = GetKeyFromUrl(fileUrl);

//                if (string.IsNullOrEmpty(key))
//                    return false;

//                var deleteRequest = new DeleteObjectRequest
//                {
//                    BucketName = _bucketName,
//                    Key = key
//                };

//                var response = await _s3Client.DeleteObjectAsync(deleteRequest);
//                return response.HttpStatusCode == System.Net.HttpStatusCode.NoContent;
//            }
//            catch (Exception ex)
//            {
//                Console.WriteLine($"Error deleting file from S3: {ex.Message}");
//                return false;
//            }
//        }

//        public string? GetFileUrl(string? key)
//        {
//            if (string.IsNullOrEmpty(key))
//            {
//                Console.WriteLine("S3Service.GetFileUrl: Key is null or empty");
//                return null;
//            }

//            // Construct the URL for the S3 object
//            var region = _configuration["AWS:Region"] ?? "eu-north-1"; // Default to Stockholm region
//            var url = $"https://{_bucketName}.s3.{region}.amazonaws.com/{key}";

//            Console.WriteLine($"S3Service.GetFileUrl: Generated URL: {url}");
//            return url;
//        }

//        private static string? GetKeyFromUrl(string? fileUrl)
//        {
//            if (string.IsNullOrEmpty(fileUrl))
//                return null;

//            // If it's already a key (not a full URL), return it
//            if (!fileUrl.StartsWith("http", StringComparison.OrdinalIgnoreCase))
//                return fileUrl;

//            // Extract the key from the URL
//            var uri = new Uri(fileUrl);
//            var key = uri.AbsolutePath;

//            // Remove the leading slash if present
//            if (key.StartsWith('/'))
//                key = key[1..]; // Use range operator instead of Substring

//            return key;
//        }
//    }
//}
