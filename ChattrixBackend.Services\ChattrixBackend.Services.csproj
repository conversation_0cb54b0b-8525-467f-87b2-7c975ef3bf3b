﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="AWSSDK.S3" Version="4.0.2.1" />
    <PackageReference Include="MailKit" Version="4.12.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.17">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.17" />
    <PackageReference Include="MimeKit" Version="4.12.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.17" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ChattrixBackend.Core\ChattrixBackend.Core.csproj" />
    <ProjectReference Include="..\ChattrixBackend.EntityFrameworkCore\ChattrixBackend.EntityFrameworkCore.csproj" />
  </ItemGroup>

</Project>
