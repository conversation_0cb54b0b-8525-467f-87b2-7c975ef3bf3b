using ChattrixBackend.Core.Entities.ChatManagement.WebSocketModel;
using System.Net.WebSockets;

namespace ChattrixBackend.Services.WebSocketServices {
    public interface IWebSocketConnectionManager {
        Task<string> AddConnectionAsync(WebSocket webSocket, string? ipAddress = null, string? deviceInfo = null);
        Task<bool> AuthenticateConnectionAsync(string connectionId, string userId);
        Task RemoveConnectionAsync(string connectionId);
        Task<WebSocketConnection?> GetConnectionAsync(string connectionId);
        Task<List<WebSocketConnection>> GetUserConnectionsAsync(string userId);
        Task<List<WebSocketConnection>> GetConversationConnectionsAsync(string conversationId);
        Task SendToConnectionAsync(string connectionId, object message);
        Task SendToUserAsync(string userId, object message);
        Task SendToConversationAsync(string conversationId, object message, string? excludeUserId = null);
        Task BroadcastAsync(object message, string? excludeUserId = null);
        Task<bool> IsUserOnlineAsync(string userId);
        Task<int> GetActiveConnectionsCountAsync();
        Task<List<string>> GetOnlineUsersAsync();
        Task CleanupInactiveConnectionsAsync();
    }
}
