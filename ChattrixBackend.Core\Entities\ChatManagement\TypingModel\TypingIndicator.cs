using System.ComponentModel.DataAnnotations;
using ChattrixBackend.Core.Entities.ChatManagement.ConversationModel;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;

namespace ChattrixBackend.Core.Entities.ChatManagement.TypingModel {
    public class TypingIndicator {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string ConversationId { get; set; } = string.Empty;

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public DateTime StartedAt { get; set; } = DateTime.UtcNow;

        [Required]
        public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual Conversation Conversation { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }
}
