import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class SecureStorageService {
  private readonly TOKEN_KEY = 'chattrix_auth_token';
  private readonly USER_INFO_KEY = 'chattrix_user_info';
  private readonly REFRESH_TOKEN_KEY = 'chattrix_refresh_token';

  /**
   * Stores the authentication token securely
   * In a production environment, consider using more secure storage methods
   */
  setToken(token: string): void {
    try {
      // For now using localStorage, but in production consider:
      // - HttpOnly cookies for better security
      // - Encrypted storage
      // - Session storage for shorter-lived tokens
      localStorage.setItem(this.TOKEN_KEY, token);
    } catch (error) {
      console.error('Failed to store token:', error);
    }
  }

  /**
   * Retrieves the authentication token
   */
  getToken(): string | null {
    try {
      return localStorage.getItem(this.TOKEN_KEY);
    } catch (error) {
      console.error('Failed to retrieve token:', error);
      return null;
    }
  }

  /**
   * Stores user information securely
   */
  setUserInfo(userInfo: any): void {
    try {
      localStorage.setItem(this.USER_INFO_KEY, JSON.stringify(userInfo));
    } catch (error) {
      console.error('Failed to store user info:', error);
    }
  }

  /**
   * Retrieves user information
   */
  getUserInfo(): any {
    try {
      const userInfo = localStorage.getItem(this.USER_INFO_KEY);
      return userInfo ? JSON.parse(userInfo) : null;
    } catch (error) {
      console.error('Failed to retrieve user info:', error);
      return null;
    }
  }

  /**
   * Stores refresh token (if implemented)
   */
  setRefreshToken(refreshToken: string): void {
    try {
      localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    } catch (error) {
      console.error('Failed to store refresh token:', error);
    }
  }

  /**
   * Retrieves refresh token
   */
  getRefreshToken(): string | null {
    try {
      return localStorage.getItem(this.REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to retrieve refresh token:', error);
      return null;
    }
  }

  /**
   * Clears all stored authentication data
   */
  clearAll(): void {
    try {
      localStorage.removeItem(this.TOKEN_KEY);
      localStorage.removeItem(this.USER_INFO_KEY);
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  }

  /**
   * Checks if storage is available
   */
  isStorageAvailable(): boolean {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Encrypts data before storing (basic implementation)
   * In production, use a proper encryption library
   */
  private encrypt(data: string): string {
    // This is a basic implementation - use proper encryption in production
    return btoa(data);
  }

  /**
   * Decrypts stored data (basic implementation)
   */
  private decrypt(encryptedData: string): string {
    try {
      return atob(encryptedData);
    } catch {
      return encryptedData; // Return as-is if decryption fails
    }
  }
}
