using ChattrixBackend.Core.Entities.ChatManagement.WebSocketModel;
using ChattrixBackend.Services.ChatServices;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;

namespace ChattrixBackend.Services.WebSocketServices {
    public class WebSocketMiddleware {
        private readonly RequestDelegate _next;
        private readonly IWebSocketConnectionManager _connectionManager;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<WebSocketMiddleware> _logger;
        private readonly TokenValidationParameters _tokenValidationParameters;

        public WebSocketMiddleware(
            RequestDelegate next,
            IWebSocketConnectionManager connectionManager,
            IServiceProvider serviceProvider,
            ILogger<WebSocketMiddleware> logger,
            TokenValidationParameters tokenValidationParameters) {
            _next = next;
            _connectionManager = connectionManager;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _tokenValidationParameters = tokenValidationParameters;
        }

        public async Task InvokeAsync(HttpContext context) {
            if (context.Request.Path == "/ws" && context.WebSockets.IsWebSocketRequest) {
                var webSocket = await context.WebSockets.AcceptWebSocketAsync();
                var connectionId = await _connectionManager.AddConnectionAsync(
                    webSocket,
                    context.Connection.RemoteIpAddress?.ToString(),
                    context.Request.Headers["User-Agent"].ToString()
                );

                await HandleWebSocketAsync(connectionId, webSocket);
            }
            else {
                await _next(context);
            }
        }

        private async Task HandleWebSocketAsync(string connectionId, WebSocket webSocket) {
            var buffer = new byte[1024 * 4];
            var isAuthenticated = false;
            var authTimeout = DateTime.UtcNow.AddMinutes(1); // 1 minute to authenticate

            try {
                while (webSocket.State == WebSocketState.Open) {
                    var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);

                    if (result.MessageType == WebSocketMessageType.Text) {
                        var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                        isAuthenticated = await ProcessMessageAsync(connectionId, message, isAuthenticated).ConfigureAwait(false);
                    }
                    else if (result.MessageType == WebSocketMessageType.Close) {
                        break;
                    }

                    // Check authentication timeout
                    if (!isAuthenticated && DateTime.UtcNow > authTimeout) {
                        await SendErrorAsync(connectionId, "Authentication timeout");
                        break;
                    }
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error handling WebSocket connection {connectionId}");
            }
            finally {
                await _connectionManager.RemoveConnectionAsync(connectionId);
            }
        }

        private async Task<bool> ProcessMessageAsync(string connectionId, string message, bool isAuthenticated) {
            try {
                var wsMessage = JsonSerializer.Deserialize<WebSocketMessage>(message);
                if (wsMessage == null) return isAuthenticated;

                switch (wsMessage.Type.ToLower()) {
                    case "authenticate":
                        return await HandleAuthenticationAsync(connectionId, wsMessage.Data);

                    case "message":
                        if (isAuthenticated) {
                            await HandleChatMessageAsync(connectionId, wsMessage.Data);
                        }
                        else {
                            await SendErrorAsync(connectionId, "Not authenticated");
                        }
                        break;

                    case "typing":
                        if (isAuthenticated) {
                            await HandleTypingAsync(connectionId, wsMessage.Data);
                        }
                        break;

                    case "presence":
                        if (isAuthenticated) {
                            await HandlePresenceAsync(connectionId, wsMessage.Data);
                        }
                        break;

                    case "message_status":
                        if (isAuthenticated) {
                            await HandleMessageStatusAsync(connectionId, wsMessage.Data);
                        }
                        break;

                    case "ping":
                        await SendPongAsync(connectionId);
                        break;

                    default:
                        await SendErrorAsync(connectionId, "Unknown message type");
                        break;
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error processing message for connection {connectionId}");
                await SendErrorAsync(connectionId, "Error processing message");
            }

            // Ensure a return value for all code paths
            return isAuthenticated;
        }

        private async Task<bool> HandleAuthenticationAsync(string connectionId, object? data) {
            try {
                var authData = JsonSerializer.Deserialize<AuthenticationMessage>(JsonSerializer.Serialize(data));
                if (authData == null || string.IsNullOrEmpty(authData.Token)) {
                    await SendErrorAsync(connectionId, "Invalid authentication data");
                    return false;
                }

                var userId = ValidateJwtToken(authData.Token);
                if (string.IsNullOrEmpty(userId)) {
                    await SendErrorAsync(connectionId, "Invalid token");
                    return false;
                }

                var success = await _connectionManager.AuthenticateConnectionAsync(connectionId, userId);
                if (success) {
                    await _connectionManager.SendToConnectionAsync(connectionId, new WebSocketMessage {
                        Type = "authenticated",
                        Data = new { userId, connectionId }
                    });
                    return true;
                }
                else {
                    await SendErrorAsync(connectionId, "Authentication failed");
                    return false;
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error during authentication for connection {connectionId}");
                await SendErrorAsync(connectionId, "Authentication error");
                return false;
            }
        }

        private async Task HandleChatMessageAsync(string connectionId, object? data) {
            try {
                var chatMessage = JsonSerializer.Deserialize<ChatMessage>(JsonSerializer.Serialize(data));
                if (chatMessage == null) return;

                var connection = await _connectionManager.GetConnectionAsync(connectionId);
                if (connection == null || !connection.IsAuthenticated) return;

                using var scope = _serviceProvider.CreateScope();
                var chatService = scope.ServiceProvider.GetRequiredService<IChatService>();

                var result = await chatService.SendMessageAsync(
                    connection.UserId,
                    chatMessage.ConversationId,
                    chatMessage.Content,
                    chatMessage.Type,
                    chatMessage.ReplyToMessageId,
                    chatMessage.ClientMessageId
                );

                if (result.IsSuccess && result.Data != null) {
                    // Send to all conversation participants
                    await _connectionManager.SendToConversationAsync(
                        chatMessage.ConversationId,
                        new WebSocketMessage {
                            Type = "message_received",
                            Data = result.Data
                        }
                    );
                }
                else {
                    await SendErrorAsync(connectionId, result.Message);
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error handling chat message for connection {connectionId}");
                await SendErrorAsync(connectionId, "Error sending message");
            }
        }

        private async Task HandleTypingAsync(string connectionId, object? data) {
            try {
                var typingMessage = JsonSerializer.Deserialize<TypingMessage>(JsonSerializer.Serialize(data));
                if (typingMessage == null) return;

                var connection = await _connectionManager.GetConnectionAsync(connectionId);
                if (connection == null || !connection.IsAuthenticated) return;

                using var scope = _serviceProvider.CreateScope();
                var chatService = scope.ServiceProvider.GetRequiredService<IChatService>();

                await chatService.UpdateTypingStatusAsync(
                    connection.UserId,
                    typingMessage.ConversationId,
                    typingMessage.IsTyping
                );

                // Broadcast typing status to other conversation participants
                await _connectionManager.SendToConversationAsync(
                    typingMessage.ConversationId,
                    new WebSocketMessage {
                        Type = "user_typing",
                        Data = new {
                            userId = connection.UserId,
                            conversationId = typingMessage.ConversationId,
                            isTyping = typingMessage.IsTyping
                        }
                    },
                    connection.UserId // Exclude the sender
                );
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error handling typing indicator for connection {connectionId}");
            }
        }

        private async Task HandlePresenceAsync(string connectionId, object? data) {
            try {
                var presenceMessage = JsonSerializer.Deserialize<PresenceMessage>(JsonSerializer.Serialize(data));
                if (presenceMessage == null) return;

                var connection = await _connectionManager.GetConnectionAsync(connectionId);
                if (connection == null || !connection.IsAuthenticated) return;

                using var scope = _serviceProvider.CreateScope();
                var chatService = scope.ServiceProvider.GetRequiredService<IChatService>();

                await chatService.UpdateUserPresenceAsync(connection.UserId, presenceMessage.Status);

                // Broadcast presence update
                await _connectionManager.BroadcastAsync(
                    new WebSocketMessage {
                        Type = "user_presence",
                        Data = new {
                            userId = connection.UserId,
                            status = presenceMessage.Status
                        }
                    },
                    connection.UserId
                );
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error handling presence update for connection {connectionId}");
            }
        }

        private async Task HandleMessageStatusAsync(string connectionId, object? data) {
            try {
                var statusUpdate = JsonSerializer.Deserialize<MessageStatusUpdate>(JsonSerializer.Serialize(data));
                if (statusUpdate == null) return;

                var connection = await _connectionManager.GetConnectionAsync(connectionId);
                if (connection == null || !connection.IsAuthenticated) return;

                using var scope = _serviceProvider.CreateScope();
                var chatService = scope.ServiceProvider.GetRequiredService<IChatService>();

                await chatService.UpdateMessageStatusAsync(
                    statusUpdate.MessageId,
                    connection.UserId,
                    statusUpdate.Status
                );
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error handling message status update for connection {connectionId}");
            }
        }

        private async Task SendPongAsync(string connectionId) {
            await _connectionManager.SendToConnectionAsync(connectionId, new WebSocketMessage {
                Type = "pong"
            });
        }

        private async Task SendErrorAsync(string connectionId, string error) {
            await _connectionManager.SendToConnectionAsync(connectionId, new WebSocketMessage {
                Type = "error",
                Data = new ErrorResponse { Error = "Error", Message = error }
            });
        }

        private string? ValidateJwtToken(string token) {
            try {
                var tokenHandler = new JwtSecurityTokenHandler();
                var principal = tokenHandler.ValidateToken(token, _tokenValidationParameters, out var validatedToken);
                return principal.FindFirst("sub")?.Value ?? principal.FindFirst("nameid")?.Value;
            }
            catch {
                return null;
            }
        }
    }
}
