import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

// Material UI Modules
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';

// Layout Module
import { LayoutModule } from '../../Layout/layout.module';

import { ChattrixRoutingModule } from './chattrix-routing.module';
import { DashboardComponent } from '../dashboard/dashboard.component';
import { ChatWindowComponent } from './Pages/chat-window/chat-window.component';
import { ChatUsersComponent } from './Pages/chat-users/chat-users.component';

@NgModule({
  declarations: [ChatWindowComponent, ChatUsersComponent],
  imports: [
    CommonModule,
    ChattrixRoutingModule,
    LayoutModule,
    // Material UI Modules
    MatIconModule,
    MatButtonModule,
    MatCardModule,
  ],
})
export class ChattrixModule {}
