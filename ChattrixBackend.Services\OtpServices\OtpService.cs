﻿using ChattrixBackend.Core.Entities.UserManagement.UserOtpModel;
using ChattrixBackend.EntityFramworkCore.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace ChattrixBackend.Services.OtpServices {
    public class OtpService : IOtpService {

        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;

        public OtpService(ApplicationDbContext context, IConfiguration configuration) {
            _context = context;
            _configuration = configuration;
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        public async Task StoreOtp(string userId, string otp) {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(otp)) {
                throw new ArgumentException("UserId and OTP cannot be null or empty.");
            }

            var expiry = DateTime.UtcNow.AddMinutes(5);

            try {
                var existing = await _context.UserOtps.FindAsync(userId);
                if (existing != null) {
                    existing.Otp = otp;
                    existing.ExpiryTime = expiry;
                }
                else {
                    var newOtp = new UserOtp {
                        UserId = userId,
                        Otp = otp,
                        ExpiryTime = expiry
                    };
                    await _context.UserOtps.AddAsync(newOtp);
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex) {
                // Log the exception (use a logging framework like Serilog or NLog)
                Console.WriteLine($"Error storing OTP for UserId: {userId}. Exception: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> VerifyOtp(string userId, string otp) {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(otp)) {
                throw new ArgumentException("UserId and OTP cannot be null or empty.");
            }

            try {
                var record = await _context.UserOtps.FindAsync(userId);
                if (record != null && record.Otp == otp && record.ExpiryTime > DateTime.UtcNow) {
                    return true;
                }
                return false;
            }
            catch (Exception ex) {
                // Log the exception
                Console.WriteLine($"Error verifying OTP for UserId: {userId}. Exception: {ex.Message}");
                throw;
            }
        }

        public async Task MarkOtpVerified(string userId) {
            if (string.IsNullOrEmpty(userId)) {
                throw new ArgumentException("UserId cannot be null or empty.");
            }

            try {
                var record = await _context.UserOtps.FindAsync(userId);
                if (record != null) {
                    record.LastVerifiedTime = DateTime.UtcNow;
                    _context.UserOtps.Update(record);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex) {
                // Log the exception
                Console.WriteLine($"Error marking OTP as verified for UserId: {userId}. Exception: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> IsOtpRecentlyVerified(string userId) {
            if (string.IsNullOrEmpty(userId)) {
                throw new ArgumentException("UserId cannot be null or empty.");
            }

            try {
                var lastVerifiedTime = await _context.UserOtps
                    .Where(u => u.UserId == userId)
                    .Select(u => u.LastVerifiedTime)
                    .FirstOrDefaultAsync();

                if (lastVerifiedTime.HasValue) {
                    return DateTime.UtcNow - lastVerifiedTime.Value < TimeSpan.FromHours(4);
                }
                return false;
            }
            catch (Exception ex) {
                // Log the exception
                Console.WriteLine($"Error checking OTP verification status for UserId: {userId}. Exception: {ex.Message}");
                throw;
            }
        }

        //public async Task CleanUpExpiredOtps() {
        //    const string query = @"
        //        DELETE FROM UserOtp
        //        WHERE ExpiryTime < @CurrentTime";

        //    try {
        //        using var connection = new SqlConnection(_connectionString);
        //        await connection.ExecuteAsync(query, new { CurrentTime = DateTime.UtcNow });
        //        Console.WriteLine("Expired OTPs cleaned up successfully.");
        //    }
        //    catch (Exception ex) {
        //        // Log the exception
        //        Console.WriteLine($"Error cleaning up expired OTPs. Exception: {ex.Message}");
        //        throw;
        //    }
        //}
    }
}
