/* Delete User Dialog */
.delete-user-dialog {
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Dialog Header */
.dialog-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.dialog-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;

  mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }
}

/* Dialog Content */
.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
}

/* User Info Section */
.user-info-section {
  margin-bottom: var(--spacing-lg);
}

.user-summary {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--border-primary);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.user-avatar-placeholder {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #000000; /* Black background for minimalist design */
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.25rem;
  border: 2px solid var(--border-primary);
}

.user-details {
  flex: 1;
}

.user-name {
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.user-email {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin: 0 0 var(--spacing-xs) 0;
}

.user-roles {
  color: var(--text-muted);
  font-size: 0.85rem;
  margin: 0;
  font-style: italic;
}

/* Warning Section */
.warning-section {
  margin-bottom: var(--spacing-lg);
}

.warning-card {
  border-radius: var(--radius-md);

  &.warning-blocked {
    background: #ffebee;
    border: 1px solid #f44336;

    .mat-mdc-card-content {
      padding: var(--spacing-md);
    }
  }

  &.warning-danger {
    background: #fff3e0;
    border: 1px solid #ff9800;

    .mat-mdc-card-content {
      padding: var(--spacing-md);
    }
  }

  &.warning-caution {
    background: #fff8e1;
    border: 1px solid #ffc107;

    .mat-mdc-card-content {
      padding: var(--spacing-md);
    }
  }
}

.warning-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.warning-text {
  flex: 1;
}

.warning-text h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1rem;
  font-weight: 600;
}

.warning-text p {
  margin: 0;
  line-height: 1.5;
  font-size: 0.9rem;
}

.warning-blocked {
  color: #d32f2f;
}

.warning-danger {
  color: #f57c00;
}

.warning-caution {
  color: #f9a825;
}

/* Reason Section */
.reason-section {
  margin-bottom: var(--spacing-lg);
}

.reason-field {
  width: 100%;
}

/* Additional Warnings */
.additional-warnings {
  margin-bottom: var(--spacing-lg);
}

.warning-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--accent-green);

  &:last-child {
    margin-bottom: 0;
  }
}

.warning-icon {
  color: var(--accent-green);
  font-size: 18px;
  width: 18px;
  height: 18px;
  margin-top: 2px;
  flex-shrink: 0;
}

.warning-item span {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Dialog Actions */
.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.cancel-button {
  color: var(--text-secondary);

  &:hover {
    background: var(--bg-hover);
  }
}

.delete-button {
  mat-icon {
    margin-right: var(--spacing-xs);
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .delete-user-dialog {
    max-width: 95vw;
    max-height: 95vh;
  }

  .dialog-header,
  .dialog-content,
  .dialog-actions {
    padding: var(--spacing-md);
  }

  .user-summary {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .user-avatar,
  .user-avatar-placeholder {
    width: 50px;
    height: 50px;
    font-size: 1rem;
  }

  .warning-content {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .warning-item {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .dialog-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-sm);
  }

  .cancel-button,
  .delete-button {
    width: 100%;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .dialog-header,
  .dialog-actions {
    background: #f5f5f5;
    border-color: #e0e0e0;
  }

  .dialog-content {
    background: #ffffff;
  }

  .dialog-title {
    color: #333333;
  }

  .user-summary {
    background: #ffffff;
    border-color: #e0e0e0;
  }

  .user-name {
    color: #333333;
  }

  .user-email {
    color: #666666;
  }

  .user-roles {
    color: #999999;
  }

  .warning-card {
    &.warning-blocked {
      background: #ffebee;
    }

    &.warning-danger {
      background: #fff3e0;
    }

    &.warning-caution {
      background: #fff8e1;
    }
  }

  .warning-item {
    background: #f9f9f9;
  }

  .warning-item span {
    color: #666666;
  }
}
