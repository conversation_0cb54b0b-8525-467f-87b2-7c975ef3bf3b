import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { Observable, map, take } from 'rxjs';
import { AuthenticationService } from '../../Pages/authentication/Services/Authentication.service';
import { AuthStateService } from '../../Pages/authentication/Services/AuthState.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthenticationService,
    private authState: AuthStateService,
    private router: Router,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.authState.authState$.pipe(
      take(1),
      map((authState) => {
        const isAuthenticated = authState.isAuthenticated;

        if (isAuthenticated) {
          return true;
        } else {
          // Redirect to login page
          this.router.navigate(['/auth/login'], {
            queryParams: { returnUrl: state.url },
          });
          return false;
        }
      }),
    );
  }
}
