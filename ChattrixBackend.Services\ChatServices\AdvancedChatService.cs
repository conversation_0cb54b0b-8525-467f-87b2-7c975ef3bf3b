using ChattrixBackend.Core.Entities.ChatManagement.TypingModel;
using ChattrixBackend.Core.Entities.UserManagement.ResponseModel;
using ChattrixBackend.EntityFramworkCore.Data;
using ChattrixBackend.Services.WebSocketServices;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ChattrixBackend.Services.ChatServices {
    public class AdvancedChatService : IAdvancedChatService {
        private readonly ApplicationDbContext _context;
        private readonly IWebSocketConnectionManager _connectionManager;
        private readonly IMessageRoutingService _messageRoutingService;
        private readonly ILogger<AdvancedChatService> _logger;

        public AdvancedChatService(
            ApplicationDbContext context,
            IWebSocketConnectionManager connectionManager,
            IMessageRoutingService messageRoutingService,
            ILogger<AdvancedChatService> logger) {
            _context = context;
            _connectionManager = connectionManager;
            _messageRoutingService = messageRoutingService;
            _logger = logger;
        }

        public async Task<Response> StartTypingAsync(string userId, string conversationId) {
            try {
                // Verify user is participant
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You are not a participant in this conversation"
                    };
                }

                // Update or create typing indicator
                var existingIndicator = await _context.TypingIndicators
                    .FirstOrDefaultAsync(ti => ti.UserId == userId && ti.ConversationId == conversationId);

                if (existingIndicator == null) {
                    _context.TypingIndicators.Add(new TypingIndicator {
                        UserId = userId,
                        ConversationId = conversationId,
                        IsActive = true
                    });
                } else {
                    existingIndicator.IsActive = true;
                    existingIndicator.LastUpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                // Route typing indicator to other participants
                await _messageRoutingService.RouteTypingIndicatorAsync(conversationId, userId, true);

                return new Response {
                    IsSuccess = true,
                    Message = "Typing indicator started"
                };
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error starting typing indicator for user {userId} in conversation {conversationId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error starting typing indicator"
                };
            }
        }

        public async Task<Response> StopTypingAsync(string userId, string conversationId) {
            try {
                var existingIndicator = await _context.TypingIndicators
                    .FirstOrDefaultAsync(ti => ti.UserId == userId && ti.ConversationId == conversationId);

                if (existingIndicator != null) {
                    existingIndicator.IsActive = false;
                    existingIndicator.LastUpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }

                // Route typing indicator stop to other participants
                await _messageRoutingService.RouteTypingIndicatorAsync(conversationId, userId, false);

                return new Response {
                    IsSuccess = true,
                    Message = "Typing indicator stopped"
                };
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error stopping typing indicator for user {userId} in conversation {conversationId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error stopping typing indicator"
                };
            }
        }

        public async Task<Response> GetTypingUsersAsync(string conversationId, string userId) {
            try {
                // Verify user is participant
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You are not a participant in this conversation"
                    };
                }

                var typingUsers = await _context.TypingIndicators
                    .Where(ti => ti.ConversationId == conversationId && 
                               ti.IsActive && 
                               ti.UserId != userId &&
                               ti.LastUpdatedAt > DateTime.UtcNow.AddMinutes(-2)) // Consider typing expired after 2 minutes
                    .Include(ti => ti.User)
                    .Select(ti => new {
                        userId = ti.UserId,
                        userName = ti.User.FullName,
                        startedAt = ti.StartedAt,
                        lastUpdatedAt = ti.LastUpdatedAt
                    })
                    .ToListAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Typing users retrieved successfully",
                    Data = typingUsers
                };
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error getting typing users for conversation {conversationId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving typing users"
                };
            }
        }

        public async Task CleanupExpiredTypingIndicatorsAsync() {
            try {
                var expiredIndicators = await _context.TypingIndicators
                    .Where(ti => ti.IsActive && ti.LastUpdatedAt < DateTime.UtcNow.AddMinutes(-2))
                    .ToListAsync();

                foreach (var indicator in expiredIndicators) {
                    indicator.IsActive = false;
                    // Notify other participants that typing stopped
                    await _messageRoutingService.RouteTypingIndicatorAsync(indicator.ConversationId, indicator.UserId, false);
                }

                if (expiredIndicators.Any()) {
                    await _context.SaveChangesAsync();
                    _logger.LogInformation($"Cleaned up {expiredIndicators.Count} expired typing indicators");
                }
            } catch (Exception ex) {
                _logger.LogError(ex, "Error cleaning up expired typing indicators");
            }
        }

        public async Task<Response> GetMessageReadReceiptsAsync(string messageId, string userId) {
            try {
                // Verify user has access to this message
                var message = await _context.Messages
                    .Include(m => m.Conversation)
                    .ThenInclude(c => c.Participants)
                    .FirstOrDefaultAsync(m => m.Id == messageId);

                if (message == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Message not found"
                    };
                }

                var isParticipant = message.Conversation.Participants
                    .Any(p => p.UserId == userId && p.IsActive);

                if (!isParticipant) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You don't have access to this message"
                    };
                }

                var readReceipts = await _context.MessageStatuses
                    .Where(ms => ms.MessageId == messageId)
                    .Include(ms => ms.User)
                    .Select(ms => new {
                        userId = ms.UserId,
                        userName = ms.User.FullName,
                        status = ms.Status.ToString().ToLower(),
                        statusAt = ms.StatusAt
                    })
                    .ToListAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Read receipts retrieved successfully",
                    Data = readReceipts
                };
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error getting read receipts for message {messageId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving read receipts"
                };
            }
        }

        public async Task<Response> GetConversationReadStatusAsync(string conversationId, string userId) {
            try {
                // Verify user is participant
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You are not a participant in this conversation"
                    };
                }

                // Get unread message count
                var unreadCount = await _context.MessageStatuses
                    .CountAsync(ms => ms.Message.ConversationId == conversationId && 
                                    ms.UserId == userId && 
                                    ms.Status != Core.Entities.ChatManagement.MessageModel.DeliveryStatus.Read);

                // Get last read message
                var lastReadMessage = await _context.ConversationParticipants
                    .Where(cp => cp.ConversationId == conversationId && cp.UserId == userId)
                    .Include(cp => cp.LastReadMessage)
                    .Select(cp => cp.LastReadMessage != null ? new {
                        messageId = cp.LastReadMessage.Id,
                        content = cp.LastReadMessage.Content,
                        sentAt = cp.LastReadMessage.SentAt
                    } : null)
                    .FirstOrDefaultAsync();

                var readStatus = new {
                    conversationId = conversationId,
                    unreadCount = unreadCount,
                    lastReadMessage = lastReadMessage,
                    lastReadAt = participant.LastReadAt
                };

                return new Response {
                    IsSuccess = true,
                    Message = "Conversation read status retrieved successfully",
                    Data = readStatus
                };
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error getting conversation read status for {conversationId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving conversation read status"
                };
            }
        }

        public async Task<Response> SetDetailedPresenceAsync(string userId, string status, string? customMessage = null) {
            try {
                var user = await _context.Users.FindAsync(userId);
                if (user == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "User not found"
                    };
                }

                // Update user presence
                user.IsOnline = status.ToLower() == "online";
                user.LastSeen = DateTime.UtcNow;

                // Add detailed presence record
                var presence = new Core.Entities.ChatManagement.PresenceModel.UserPresence {
                    UserId = userId,
                    Status = Enum.Parse<Core.Entities.ChatManagement.PresenceModel.PresenceStatus>(status, true)
                };

                _context.UserPresences.Add(presence);
                await _context.SaveChangesAsync();

                // Route presence update
                await _messageRoutingService.RoutePresenceUpdateAsync(userId, status);

                return new Response {
                    IsSuccess = true,
                    Message = "Detailed presence updated successfully"
                };
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error setting detailed presence for user {userId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error updating detailed presence"
                };
            }
        }

        public async Task<Response> GetConversationParticipantsPresenceAsync(string conversationId, string userId) {
            try {
                // Verify user is participant
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You are not a participant in this conversation"
                    };
                }

                var participantsPresence = await _context.ConversationParticipants
                    .Where(cp => cp.ConversationId == conversationId && cp.IsActive)
                    .Include(cp => cp.User)
                    .Select(cp => new {
                        userId = cp.UserId,
                        userName = cp.User.FullName,
                        isOnline = cp.User.IsOnline,
                        lastSeen = cp.User.LastSeen,
                        currentConnectionId = cp.User.CurrentConnectionId
                    })
                    .ToListAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Participants presence retrieved successfully",
                    Data = participantsPresence
                };
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error getting participants presence for conversation {conversationId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving participants presence"
                };
            }
        }

        // Placeholder implementations for remaining methods
        public async Task<Response> BulkUpdateMessageStatusAsync(string userId, List<string> messageIds, string status) {
            throw new NotImplementedException();
        }

        public async Task<Response> GetUserDetailedPresenceAsync(string userId) {
            throw new NotImplementedException();
        }

        public async Task<Response> GetRecentlyActiveUsersAsync(string userId, int hours = 24) {
            throw new NotImplementedException();
        }

        public async Task<Response> AddMessageReactionAsync(string messageId, string userId, string reaction) {
            throw new NotImplementedException();
        }

        public async Task<Response> RemoveMessageReactionAsync(string messageId, string userId, string reaction) {
            throw new NotImplementedException();
        }

        public async Task<Response> GetMessageReactionsAsync(string messageId, string userId) {
            throw new NotImplementedException();
        }

        public async Task<Response> GetMessageThreadAsync(string messageId, string userId, int page = 1, int pageSize = 20) {
            throw new NotImplementedException();
        }

        public async Task<Response> GetThreadSummaryAsync(string messageId, string userId) {
            throw new NotImplementedException();
        }

        public async Task<Response> GetConversationStatsAsync(string conversationId, string userId) {
            throw new NotImplementedException();
        }

        public async Task<Response> GetUserChatStatsAsync(string userId, int days = 30) {
            throw new NotImplementedException();
        }

        public async Task<Response> AdvancedMessageSearchAsync(string userId, string query, MessageSearchFilters filters) {
            throw new NotImplementedException();
        }

        public async Task<Response> UpdateNotificationPreferencesAsync(string userId, string conversationId, NotificationSettings settings) {
            throw new NotImplementedException();
        }

        public async Task<Response> GetNotificationPreferencesAsync(string userId, string conversationId) {
            throw new NotImplementedException();
        }

        public async Task<Response> ScheduleMessageAsync(string userId, string conversationId, string content, DateTime scheduledTime) {
            throw new NotImplementedException();
        }

        public async Task<Response> GetScheduledMessagesAsync(string userId) {
            throw new NotImplementedException();
        }

        public async Task<Response> CancelScheduledMessageAsync(string messageId, string userId) {
            throw new NotImplementedException();
        }
    }
}
