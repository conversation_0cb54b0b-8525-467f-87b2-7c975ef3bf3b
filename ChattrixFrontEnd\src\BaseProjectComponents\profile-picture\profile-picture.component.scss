/* Profile Picture Component Styles */

.profile-card {
  max-width: 520px; // Slightly wider for profile completion

  // Specific optimizations for profile form
  .auth-header {
    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-xs);
  }

  .auth-form {
    padding: var(--spacing-sm) var(--spacing-lg);
  }

  .auth-footer {
    padding: var(--spacing-sm) var(--spacing-lg);
  }

  .auth-logo {
    width: 50px;
    height: 50px;
    margin-bottom: var(--spacing-sm);
  }

  .auth-title {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
  }

  .auth-subtitle {
    font-size: var(--font-size-xs);
    margin-bottom: 0;
  }
}

/* Profile Picture Upload Section */
.profile-picture-section {
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.profile-picture-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.profile-picture-preview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 3px dashed var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: var(--bg-secondary);

  &.clickable {
    cursor: pointer;
    user-select: none;

    &:focus {
      outline: 2px solid var(--accent-green);
      outline-offset: 2px;
    }

    &:hover {
      border-color: var(--accent-green);
      background-color: var(--bg-tertiary);
      transform: scale(1.02);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  &.has-image {
    border: 3px solid var(--accent-green);
    border-style: solid;

    &.clickable:hover {
      .overlay-content {
        opacity: 1;
        visibility: visible;
      }

      .preview-image {
        filter: brightness(0.7);
      }
    }
  }
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.upload-icon {
  font-size: 2.5rem;
  width: 2.5rem;
  height: 2.5rem;
  color: var(--text-disabled);
  transition: color 0.3s ease;
}

.upload-text {
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: color 0.3s ease;
}

.overlay-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  border-radius: 50%;
}

.change-icon {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
}

.change-text {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.remove-image-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--error);
  color: white;
  width: 28px;
  height: 28px;
  min-width: 28px;

  &:hover {
    background-color: var(--error-dark);
  }

  .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }
}

// Enhanced hover effects for clickable profile picture
.profile-picture-preview.clickable:hover {
  .placeholder-content {
    .upload-icon {
      color: var(--accent-green);
      transform: scale(1.1);
    }

    .upload-text {
      color: var(--accent-green);
    }
  }
}

.upload-hint {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin: 0;
  text-align: center;
}

/* Form Styling */
.full-width {
  width: 100%;
  margin-bottom: var(--spacing-md);
}

/* Button Group */
.button-group {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  justify-content: space-between;
}

.back-button {
  flex: 0 0 auto;
  min-width: 100px;
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-transform: none;

  .mat-icon {
    margin-right: var(--spacing-xs);
  }
}

.complete-button {
  flex: 1;
  height: 44px;
  font-size: var(--font-size-base);
  font-weight: 500;
  text-transform: none;
  border-radius: var(--radius-md);
  position: relative;

  &:disabled {
    opacity: 0.6;
  }

  .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    position: relative;
  }

  .hidden {
    visibility: hidden;
    opacity: 0;
  }
}

.button-spinner {
  position: absolute;

  transform: translate(-50%, -50%);
}

/* Footer */
.footer-text {
  margin: var(--spacing-sm) 0 0;
  text-align: center;
  color: var(--text-muted);
  font-size: var(--font-size-xs);
  line-height: 1.4;
}
