using System.ComponentModel.DataAnnotations;

namespace ChattrixBackend.Core.Entities.UserManagement.PasswordResetTokenModel {
    public class PasswordResetToken {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(6, MinimumLength = 6)]
        public string Token { get; set; } = string.Empty;

        [Required]
        public DateTime ExpiryTime { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public bool IsUsed { get; set; } = false;

        public DateTime? UsedAt { get; set; }

        // Rate limiting properties
        public DateTime? LastRequestTime { get; set; }
        public int RequestCount { get; set; } = 1;
    }
}
