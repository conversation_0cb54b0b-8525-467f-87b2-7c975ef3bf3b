// User information interface based on JWT token payload
export interface TokenPayload {
  id: string;
  FullName: string;
  Email: string;
  Roles: string | string[];
  IsActive: boolean;
  PhoneNumber?: string;
  Description?: string;
  ProfilePictureUrl?: string;
  exp: number; // Token expiration timestamp
  aud: string; // Audience
  iss: string; // Issuer
  nameid: string; // Name identifier
  name: string; // Name
  email: string; // Email
  // Additional role claim properties
  role?: string | string[]; // Standard JWT role claim
  'http://schemas.microsoft.com/ws/2008/06/identity/claims/role'?:
    | string
    | string[]; // ASP.NET Core role claim
  [key: string]: any; // Allow for additional dynamic properties
}

// Mapped user information for frontend use
export interface UserInfo {
  id: string;
  name: string;
  email: string;
  role: string | string[];
  isActive: boolean;
  phoneNumber?: string;
  description?: string;
  profilePictureUrl?: string;
}

// Authentication state interface
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserInfo | null;
  token: string | null;
  error: string | null;
}
