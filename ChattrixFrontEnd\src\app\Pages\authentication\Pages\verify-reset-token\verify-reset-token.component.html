<div class="auth-container">
  <div class="auth-card">
    <!-- Header Section -->
    <div class="auth-header">
      <h1 class="auth-title">Verify Reset Code</h1>
      <p class="auth-subtitle">
        We've sent a 5-digit verification code to <strong>{{ email }}</strong
        >. Please enter it below to verify your identity.
      </p>
    </div>

    <!-- Token Form -->
    <form [formGroup]="otpForm" (ngSubmit)="onVerifyToken()" class="auth-form">
      <!-- Token Input Fields -->
      <div class="auth-input-container">
        <div class="auth-input-wrapper">
          <input
            formControlName="otpCode"
            type="text"
            maxlength="5"
            placeholder="Verification Code"
            autocomplete="one-time-code"
            inputmode="numeric"
            pattern="[0-9]*"
            class="auth-input"
            [class.error]="
              otpForm.get('otpCode')?.invalid && otpForm.get('otpCode')?.touched
            "
            style="
              text-align: start;
              letter-spacing: 0.3em;
              font-size: 20px;
              font-weight: 600;
            "
            (input)="onOtpInput($event)"
            (paste)="onOtpPaste($event)"
          />
          <mat-icon class="auth-input-icon">verified_user</mat-icon>
        </div>

        <!-- Error Message -->
        <div
          class="auth-error-message"
          *ngIf="otpForm.invalid && otpForm.touched"
        >
          <mat-icon>error</mat-icon>
          Please enter a valid 5-digit verification code
        </div>
      </div>

      <!-- Verify Button -->
      <button
        mat-raised-button
        type="submit"
        class="auth-button full-width"
        [disabled]="otpForm.invalid || isLoading"
      >
        <div class="button-content">
          <mat-spinner
            *ngIf="isLoading"
            diameter="20"
            class="button-spinner"
          ></mat-spinner>
          <span [class.hidden]="isLoading">Verify Code</span>
        </div>
      </button>

      <!-- Resend Section -->
      <div class="resend-section">
        <p class="resend-text">Didn't receive the code?</p>

        <button
          *ngIf="!canResend"
          mat-button
          type="button"
          class="resend-button disabled"
          disabled
        >
          <mat-icon>schedule</mat-icon>
          Resend in {{ timerDisplay }}
        </button>

        <button
          *ngIf="canResend"
          mat-button
          type="button"
          class="resend-button"
          (click)="onResendCode()"
          [disabled]="isResending"
        >
          <div class="button-content">
            <mat-spinner
              *ngIf="isResending"
              diameter="16"
              class="resend-spinner"
            ></mat-spinner>
            <mat-icon *ngIf="!isResending" [class.hidden]="isResending"
              >refresh</mat-icon
            >
            <span [class.hidden]="isResending">Resend Code</span>
          </div>
        </button>
      </div>
    </form>

    <!-- Footer Section -->
    <div class="auth-footer">
      <div class="back-to-login">
        <button mat-button (click)="onBackToLogin()" class="back-button">
          <mat-icon>arrow_back</mat-icon>
          Back to Login
        </button>
      </div>
    </div>
  </div>
</div>
