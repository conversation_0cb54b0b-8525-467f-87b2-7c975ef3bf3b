using AutoMapper;
using ChattrixBackend.EntityFramworkCore.Data;
using ChattrixBackend.Services.ChatServices;
using ChattrixBackend.Services.S3Services;
using ChattrixBackend.Services.WebSocketServices;
using ChattrixBackend.Services.SecurityServices;
using ChattrixBackend.Tests;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;

namespace ChattrixBackend.Tests {
    /// <summary>
    /// Validation class to test ChatService implementation
    /// This is not a unit test but a validation helper to ensure the service compiles and basic functionality works
    /// </summary>
    public class ChatServiceValidation {
        public static void ValidateServiceImplementation() {
            // This method validates that all dependencies can be resolved and the service can be instantiated

            // Create service collection for dependency injection validation
            var services = new ServiceCollection();

            // Add required services
            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseInMemoryDatabase("TestDb"));

            services.AddLogging(builder => builder.AddConsole());
            services.AddMemoryCache();
            services.AddAutoMapper(typeof(ChatService));

            // Add chat services
            services.AddScoped<IChatService, ChatService>();
            services.AddScoped<IAdvancedChatService, AdvancedChatService>();
            services.AddScoped<IMessageRoutingService, MessageRoutingService>();
            services.AddSingleton<IWebSocketConnectionManager, WebSocketConnectionManager>();
            services.AddScoped<IChatAuthorizationService, ChatAuthorizationService>();
            services.AddSingleton<IRateLimitingService, RateLimitingService>();

            // Add mock S3 service
            services.AddScoped<IS3Service, MockS3Service>();

            // Build service provider
            var serviceProvider = services.BuildServiceProvider();

            try {
                // Test service resolution
                using var scope = serviceProvider.CreateScope();
                var chatService = scope.ServiceProvider.GetRequiredService<IChatService>();
                var advancedChatService = scope.ServiceProvider.GetRequiredService<IAdvancedChatService>();
                var messageRoutingService = scope.ServiceProvider.GetRequiredService<IMessageRoutingService>();
                var connectionManager = serviceProvider.GetRequiredService<IWebSocketConnectionManager>();
                var authService = scope.ServiceProvider.GetRequiredService<IChatAuthorizationService>();
                var rateLimitService = serviceProvider.GetRequiredService<IRateLimitingService>();

                // Validate that all interface methods are implemented
                ValidateInterfaceImplementation(chatService);

                Console.WriteLine("✅ ChatService validation completed successfully!");
                Console.WriteLine("✅ All interface methods are properly implemented");
                Console.WriteLine("✅ All dependencies can be resolved");
                Console.WriteLine("✅ Service lifetime configurations are correct");
            } catch (Exception ex) {
                Console.WriteLine($"❌ Dependency injection validation failed: {ex.Message}");
                throw;
            } finally {
                serviceProvider.Dispose();
            }
        }

        private static void ValidateInterfaceImplementation(IChatService chatService) {
            // This validates that all interface methods exist and have correct signatures
            var interfaceType = typeof(IChatService);
            var implementationType = typeof(ChatService);

            var interfaceMethods = interfaceType.GetMethods();

            foreach (var method in interfaceMethods) {
                var implementationMethod = implementationType.GetMethod(method.Name,
                    method.GetParameters().Select(p => p.ParameterType).ToArray());

                if (implementationMethod == null) {
                    throw new InvalidOperationException($"Method {method.Name} is not implemented in ChatService");
                }

                if (implementationMethod.ReturnType != method.ReturnType) {
                    throw new InvalidOperationException($"Method {method.Name} has incorrect return type");
                }
            }
        }
    }

    // Mock S3 Service for validation
    public class MockS3Service : IS3Service {
        public Task<string?> UploadFileAsync(Microsoft.AspNetCore.Http.IFormFile? file, string? folderName) {
            return Task.FromResult<string?>("mock-file-key");
        }

        public Task<bool> DeleteFileAsync(string? fileUrl) {
            return Task.FromResult(true);
        }

        public string? GetFileUrl(string? key) {
            return $"https://mock-bucket.s3.amazonaws.com/{key}";
        }
    }
}

// Extension class for validation
public static class ChatServiceExtensions {
    public static IServiceCollection ValidateChatImplementation(this IServiceCollection services) {
        // This can be called during startup to validate the chat implementation
        ChatServiceValidation.ValidateServiceImplementation();
        return services;
    }
}
