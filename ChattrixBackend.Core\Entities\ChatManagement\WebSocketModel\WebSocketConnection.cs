using System.Net.WebSockets;
using System.Text;
using System.Text.Json;

namespace ChattrixBackend.Core.Entities.ChatManagement.WebSocketModel {
    public class WebSocketConnection {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string UserId { get; set; } = string.Empty;
        public WebSocket WebSocket { get; set; } = null!;
        public DateTime ConnectedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastPingAt { get; set; } = DateTime.UtcNow;
        public bool IsAuthenticated { get; set; } = false;
        public string? DeviceInfo { get; set; }
        public string? IpAddress { get; set; }

        public async Task SendAsync(object message) {
            if (WebSocket.State == WebSocketState.Open) {
                var json = JsonSerializer.Serialize(message);
                var buffer = Encoding.UTF8.GetBytes(json);
                await WebSocket.SendAsync(
                    new ArraySegment<byte>(buffer),
                    WebSocketMessageType.Text,
                    true,
                    CancellationToken.None
                );
            }
        }

        public async Task SendAsync(string message) {
            if (WebSocket.State == WebSocketState.Open) {
                var buffer = Encoding.UTF8.GetBytes(message);
                await WebSocket.SendAsync(
                    new ArraySegment<byte>(buffer),
                    WebSocketMessageType.Text,
                    true,
                    CancellationToken.None
                );
            }
        }

        public async Task CloseAsync(WebSocketCloseStatus closeStatus = WebSocketCloseStatus.NormalClosure, string? statusDescription = null) {
            if (WebSocket.State == WebSocketState.Open || WebSocket.State == WebSocketState.CloseReceived) {
                await WebSocket.CloseAsync(closeStatus, statusDescription, CancellationToken.None);
            }
        }
    }
}
