using ChattrixBackend.Core.Entities.UserManagement.PasswordResetTokenModel;
using ChattrixBackend.EntityFramworkCore.Data;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;

namespace ChattrixBackend.Services.PasswordResetServices {
    public class PasswordResetService : IPasswordResetService {
        private readonly ApplicationDbContext _context;
        private const int TOKEN_EXPIRY_MINUTES = 15;
        private const int RATE_LIMIT_MINUTES = 5;

        public PasswordResetService(ApplicationDbContext context) {
            _context = context;
        }

        public async Task<string> GenerateResetTokenAsync(string userId, string email) {
            // Generate a secure 6-digit token
            var token = GenerateSecureToken();

            // Check if there's an existing token for this email
            var existingToken = await _context.PasswordResetTokens
                .FirstOrDefaultAsync(t => t.Email == email && !t.IsUsed);

            if (existingToken != null) {
                // Update existing token
                existingToken.Token = token;
                existingToken.ExpiryTime = DateTime.UtcNow.AddMinutes(TOKEN_EXPIRY_MINUTES);
                existingToken.CreatedAt = DateTime.UtcNow;
                existingToken.LastRequestTime = DateTime.UtcNow;
                existingToken.RequestCount++;
            }
            else {
                // Create new token
                var resetToken = new PasswordResetToken {
                    UserId = userId,
                    Email = email,
                    Token = token,
                    ExpiryTime = DateTime.UtcNow.AddMinutes(TOKEN_EXPIRY_MINUTES),
                    CreatedAt = DateTime.UtcNow,
                    LastRequestTime = DateTime.UtcNow,
                    RequestCount = 1
                };

                await _context.PasswordResetTokens.AddAsync(resetToken);
            }

            await _context.SaveChangesAsync();
            return token;
        }

        public async Task<bool> VerifyResetTokenAsync(string email, string token) {
            var resetToken = await GetValidTokenAsync(email, token);
            return resetToken != null;
        }

        public async Task<bool> IsTokenValidAsync(string email, string token) {
            return await VerifyResetTokenAsync(email, token);
        }

        public async Task MarkTokenAsUsedAsync(string email, string token) {
            var resetToken = await _context.PasswordResetTokens
                .FirstOrDefaultAsync(t => t.Email == email && t.Token == token && !t.IsUsed);

            if (resetToken != null) {
                resetToken.IsUsed = true;
                resetToken.UsedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> CanRequestResetAsync(string email) {
            var lastRequest = await _context.PasswordResetTokens
                .Where(t => t.Email == email)
                .OrderByDescending(t => t.CreatedAt)
                .FirstOrDefaultAsync();

            if (lastRequest == null) {
                return true;
            }

            // Check rate limiting - allow new request only after RATE_LIMIT_MINUTES
            var timeSinceLastRequest = DateTime.UtcNow - lastRequest.CreatedAt;
            return timeSinceLastRequest.TotalMinutes >= RATE_LIMIT_MINUTES;
        }

        public async Task CleanupExpiredTokensAsync() {
            var expiredTokens = await _context.PasswordResetTokens
                .Where(t => t.ExpiryTime < DateTime.UtcNow || t.IsUsed)
                .ToListAsync();

            if (expiredTokens.Any()) {
                _context.PasswordResetTokens.RemoveRange(expiredTokens);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<PasswordResetToken?> GetValidTokenAsync(string email, string token) {
            return await _context.PasswordResetTokens
                .FirstOrDefaultAsync(t =>
                    t.Email == email &&
                    t.Token == token &&
                    !t.IsUsed &&
                    t.ExpiryTime > DateTime.UtcNow);
        }

        private string GenerateSecureToken() {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[4];
            rng.GetBytes(bytes);

            // Convert to 6-digit number
            var number = Math.Abs(BitConverter.ToInt32(bytes, 0)) % 1000000;
            return number.ToString("D6");
        }
    }
}
