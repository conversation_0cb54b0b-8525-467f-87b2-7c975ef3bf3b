<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h1 class="login-title">Welcome Back</h1>
      <p class="login-subtitle">Sign in to your account</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="login-form">
      <!-- Email Field -->
      <div class="auth-input-container">
        <div class="auth-input-wrapper">
          <input
            type="email"
            formControlName="email"
            placeholder="Email"
            autocomplete="email"
            class="auth-input"
            [class.error]="emailControl?.invalid && emailControl?.touched"
          />
          <mat-icon class="auth-input-icon">email</mat-icon>
        </div>
        <div
          *ngIf="emailControl?.hasError('required') && emailControl?.touched"
          class="auth-error-message"
        >
          <mat-icon>error</mat-icon>
          Email is required
        </div>
        <div
          *ngIf="emailControl?.hasError('email') && emailControl?.touched"
          class="auth-error-message"
        >
          <mat-icon>error</mat-icon>
          Please enter a valid email address
        </div>
      </div>

      <!-- Password Field -->
      <div class="auth-input-container">
        <div class="auth-input-wrapper">
          <input
            [type]="hidePassword ? 'password' : 'text'"
            formControlName="password"
            placeholder="Password"
            autocomplete="current-password"
            class="auth-input"
            [class.error]="passwordControl?.invalid && passwordControl?.touched"
            style="padding-right: 48px"
          />
          <mat-icon class="auth-input-icon">lock</mat-icon>
          <button
            type="button"
            (click)="togglePasswordVisibility()"
            class="auth-password-toggle"
            [attr.aria-label]="'Toggle password visibility'"
            [attr.aria-pressed]="!hidePassword"
          >
            <mat-icon>{{
              hidePassword ? "visibility_off" : "visibility"
            }}</mat-icon>
          </button>
        </div>
        <div
          *ngIf="
            passwordControl?.hasError('required') && passwordControl?.touched
          "
          class="auth-error-message"
        >
          <mat-icon>error</mat-icon>
          Password is required
        </div>
        <div
          *ngIf="
            passwordControl?.hasError('minlength') && passwordControl?.touched
          "
          class="auth-error-message"
        >
          <mat-icon>error</mat-icon>
          Password must be at least 6 characters long
        </div>
      </div>

      <!-- <div class="form-options">
        <mat-checkbox formControlName="rememberMe" class="remember-checkbox">
          Remember me
        </mat-checkbox>
        <a href="#" class="forgot-password">Forgot password?</a>
      </div> -->

      <button
        mat-raised-button
        type="submit"
        class="login-button"
        [disabled]="loginForm.invalid || isLoading"
      >
        <mat-spinner
          *ngIf="isLoading"
          diameter="20"
          class="spinner"
        ></mat-spinner>
        <span *ngIf="!isLoading">Sign In</span>
      </button>
    </form>

    <div class="login-footer">
      <div class="forgot-password-container">
        <button
          mat-button
          type="button"
          class="forgot-password"
          (click)="onForgotPassword()"
        >
          Forgot password?
        </button>
      </div>
    </div>
  </div>
</div>
