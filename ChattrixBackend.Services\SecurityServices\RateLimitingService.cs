using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace ChattrixBackend.Services.SecurityServices {
    public interface IRateLimitingService {
        Task<bool> IsAllowedAsync(string userId, string action, int maxRequests = 10, TimeSpan? timeWindow = null);
        Task<RateLimitInfo> GetRateLimitInfoAsync(string userId, string action);
        Task ResetRateLimitAsync(string userId, string action);
    }

    public class RateLimitInfo {
        public int RequestsRemaining { get; set; }
        public DateTime ResetTime { get; set; }
        public bool IsLimited { get; set; }
    }

    public class RateLimitingService : IRateLimitingService {
        private readonly IMemoryCache _cache;
        private readonly ILogger<RateLimitingService> _logger;

        // Default rate limits
        private readonly Dictionary<string, (int maxRequests, TimeSpan timeWindow)> _defaultLimits = new() {
            { "send_message", (30, TimeSpan.FromMinutes(1)) },
            { "create_conversation", (5, TimeSpan.FromMinutes(1)) },
            { "upload_file", (10, TimeSpan.FromMinutes(1)) },
            { "typing_indicator", (60, TimeSpan.FromMinutes(1)) },
            { "presence_update", (20, TimeSpan.FromMinutes(1)) },
            { "search", (20, TimeSpan.FromMinutes(1)) }
        };

        public RateLimitingService(IMemoryCache cache, ILogger<RateLimitingService> logger) {
            _cache = cache;
            _logger = logger;
        }

        public async Task<bool> IsAllowedAsync(string userId, string action, int maxRequests = 10, TimeSpan? timeWindow = null) {
            try {
                var key = $"rate_limit:{userId}:{action}";

                // Use default limits if not specified
                if (_defaultLimits.TryGetValue(action, out var defaultLimit)) {
                    maxRequests = defaultLimit.maxRequests;
                    timeWindow = defaultLimit.timeWindow;
                }

                timeWindow ??= TimeSpan.FromMinutes(1);

                var requestInfo = _cache.Get<RequestInfo>(key);
                var now = DateTime.UtcNow;

                if (requestInfo == null) {
                    // First request
                    requestInfo = new RequestInfo {
                        Count = 1,
                        WindowStart = now
                    };
                    _cache.Set(key, requestInfo, timeWindow.Value);
                    return true;
                }

                // Check if we're still in the same time window
                if (now - requestInfo.WindowStart < timeWindow.Value) {
                    if (requestInfo.Count >= maxRequests) {
                        _logger.LogWarning($"Rate limit exceeded for user {userId} on action {action}. Count: {requestInfo.Count}, Max: {maxRequests}");
                        return false;
                    }

                    requestInfo.Count++;
                    _cache.Set(key, requestInfo, timeWindow.Value);
                    return true;
                } else {
                    // New time window
                    requestInfo = new RequestInfo {
                        Count = 1,
                        WindowStart = now
                    };
                    _cache.Set(key, requestInfo, timeWindow.Value);
                    return true;
                }
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error checking rate limit for user {userId} and action {action}");
                // Allow request on error to avoid blocking legitimate users
                return true;
            }
        }

        public async Task<RateLimitInfo> GetRateLimitInfoAsync(string userId, string action) {
            try {
                var key = $"rate_limit:{userId}:{action}";
                var requestInfo = _cache.Get<RequestInfo>(key);

                if (requestInfo == null) {
                    return new RateLimitInfo {
                        RequestsRemaining = _defaultLimits.TryGetValue(action, out var limit) ? limit.maxRequests : 10,
                        ResetTime = DateTime.UtcNow.Add(_defaultLimits.TryGetValue(action, out var timeLimit) ? timeLimit.timeWindow : TimeSpan.FromMinutes(1)),
                        IsLimited = false
                    };
                }

                var maxRequests = _defaultLimits.TryGetValue(action, out var defaultLimit) ? defaultLimit.maxRequests : 10;
                var timeWindow = _defaultLimits.TryGetValue(action, out var windowLimit) ? windowLimit.timeWindow : TimeSpan.FromMinutes(1);

                return new RateLimitInfo {
                    RequestsRemaining = Math.Max(0, maxRequests - requestInfo.Count),
                    ResetTime = requestInfo.WindowStart.Add(timeWindow),
                    IsLimited = requestInfo.Count >= maxRequests
                };
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error getting rate limit info for user {userId} and action {action}");
                return new RateLimitInfo {
                    RequestsRemaining = 10,
                    ResetTime = DateTime.UtcNow.AddMinutes(1),
                    IsLimited = false
                };
            }
        }

        public async Task ResetRateLimitAsync(string userId, string action) {
            try {
                var key = $"rate_limit:{userId}:{action}";
                _cache.Remove(key);
                _logger.LogInformation($"Rate limit reset for user {userId} and action {action}");
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error resetting rate limit for user {userId} and action {action}");
            }
        }

        private class RequestInfo {
            public int Count { get; set; }
            public DateTime WindowStart { get; set; }
        }
    }
}
