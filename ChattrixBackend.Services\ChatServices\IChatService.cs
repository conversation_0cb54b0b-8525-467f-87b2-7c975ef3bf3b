using ChattrixBackend.Core.Entities.UserManagement.ResponseModel;
using ChattrixBackend.Core.Entities.ChatManagement.ConversationModel;
using ChattrixBackend.Core.Entities.ChatManagement.MessageModel;

namespace ChattrixBackend.Services.ChatServices {
    public interface IChatService {
        // Message operations
        Task<Response> SendMessageAsync(string senderId, string conversationId, string content, string messageType = "text", string? replyToMessageId = null, string? clientMessageId = null);
        Task<Response> EditMessageAsync(string messageId, string userId, string newContent);
        Task<Response> DeleteMessageAsync(string messageId, string userId);
        Task<Response> GetMessagesAsync(string conversationId, string userId, int page = 1, int pageSize = 50);
        Task<Response> GetMessageAsync(string messageId, string userId);

        // Conversation operations
        Task<Response> CreatePrivateConversationAsync(string userId1, string userId2);
        Task<Response> CreateGroupConversationAsync(string creatorId, string name, string? description = null, List<string>? participantIds = null);
        Task<Response> GetConversationsAsync(string userId, int page = 1, int pageSize = 20);
        Task<Response> GetConversationAsync(string conversationId, string userId);
        Task<Response> UpdateConversationAsync(string conversationId, string userId, string? name = null, string? description = null);
        Task<Response> DeleteConversationAsync(string conversationId, string userId);

        // Participant operations
        Task<Response> AddParticipantAsync(string conversationId, string adminId, string participantId);
        Task<Response> RemoveParticipantAsync(string conversationId, string adminId, string participantId);
        Task<Response> LeaveConversationAsync(string conversationId, string userId);
        Task<Response> UpdateParticipantRoleAsync(string conversationId, string adminId, string participantId, ParticipantRole role);
        Task<Response> GetConversationParticipantsAsync(string conversationId, string userId);

        // Message status operations
        Task<Response> UpdateMessageStatusAsync(string messageId, string userId, string status);
        Task<Response> MarkMessagesAsReadAsync(string conversationId, string userId, List<string> messageIds);
        Task<Response> GetUnreadMessagesCountAsync(string userId);

        // Typing and presence operations
        Task<Response> UpdateTypingStatusAsync(string userId, string conversationId, bool isTyping);
        Task<Response> UpdateUserPresenceAsync(string userId, string status);
        Task<Response> GetUserPresenceAsync(string userId);
        Task<Response> GetOnlineUsersAsync();

        // Search operations
        Task<Response> SearchMessagesAsync(string userId, string query, string? conversationId = null, int page = 1, int pageSize = 20);
        Task<Response> SearchConversationsAsync(string userId, string query, int page = 1, int pageSize = 20);

        // File operations
        Task<Response> UploadFileAsync(string userId, string conversationId, Stream fileStream, string fileName, string contentType);
    }
}
