// Import shared authentication theme and variables
@use "../../../../Layout/SCSS/shared-auth-theme.scss" as *;
// Login-specific styles using shared theme classes
.login-container {
  @extend .auth-container;
}

.login-card {
  @extend .auth-card;
}

.login-header {
  @extend .auth-header;
}

.login-title {
  @extend .auth-title;
}

.login-subtitle {
  @extend .auth-subtitle;
}

.login-form {
  @extend .auth-form;
}

.form-field {
  width: 100%;
}

// Material UI Form Field Overrides - Fixed Authentication Theme
.form-field ::ng-deep .mat-mdc-form-field-outline {
  color: #e0e0e0 !important;
}

.form-field ::ng-deep .mat-mdc-form-field-focus-overlay {
  background-color: transparent !important;
}

.form-field ::ng-deep .mat-mdc-form-field-outline-thick {
  color: #1a1a1a !important;
}

.form-field ::ng-deep .mat-mdc-floating-label {
  color: #666 !important;
}

.form-field ::ng-deep .mat-mdc-floating-label.mdc-floating-label--float-above {
  color: #1a1a1a !important;
}

.form-field ::ng-deep .mat-mdc-input-element {
  color: #1a1a1a !important;
}

.form-field ::ng-deep .mat-mdc-input-element::placeholder {
  color: #999 !important;
}

// Additional Material UI overrides to ensure focus states work properly
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__leading,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__notch,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__trailing {
  border-color: #e0e0e0 !important;
}

.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled):hover
  .mdc-notched-outline
  .mdc-notched-outline__leading,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled):hover
  .mdc-notched-outline
  .mdc-notched-outline__notch,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled):hover
  .mdc-notched-outline
  .mdc-notched-outline__trailing {
  border-color: #999 !important;
}

.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(
    .mdc-text-field--disabled
  ).mdc-text-field--focused
  .mdc-notched-outline__leading,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(
    .mdc-text-field--disabled
  ).mdc-text-field--focused
  .mdc-notched-outline__notch,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(
    .mdc-text-field--disabled
  ).mdc-text-field--focused
  .mdc-notched-outline__trailing {
  border-color: #1a1a1a !important;
  border-width: 2px !important;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: -8px 0;
}

.remember-checkbox ::ng-deep .mat-mdc-checkbox-frame {
  border-color: #ccc;
}

.remember-checkbox ::ng-deep .mat-mdc-checkbox-checkmark {
  color: #fff;
}

.remember-checkbox ::ng-deep .mat-mdc-checkbox-mixedmark {
  color: #fff;
}

.remember-checkbox
  ::ng-deep
  .mdc-checkbox__native-control:enabled:checked
  ~ .mdc-checkbox__background {
  background-color: #1a1a1a;
  border-color: #1a1a1a;
}

.remember-checkbox ::ng-deep .mat-mdc-checkbox-touch-target {
  width: 40px;
  height: 40px;
}

.forgot-password {
  color: #666;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.forgot-password:hover {
  color: #1a1a1a;
  text-decoration: underline;
}

.login-button {
  @extend .auth-button;
}

.spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.spinner ::ng-deep circle {
  stroke: white;
}

.login-footer {
  @extend .auth-footer;
}

.forgot-password {
  @extend .auth-link;
}

// Responsive design is handled by the shared auth theme
// Additional login-specific responsive styles can be added here if needed
