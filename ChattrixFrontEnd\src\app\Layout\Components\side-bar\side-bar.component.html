<div
  class="sidebar-container"
  [class.collapsed]="isCollapsed"
  *ngIf="userProfile$ | async as userProfile"
>
  <!-- Sidebar Toggle Button -->
  <button
    class="sidebar-toggle"
    (click)="toggleSidebar()"
    mat-icon-button
    [matTooltip]="isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'"
    matTooltipPosition="right"
  >
    <mat-icon>{{ isCollapsed ? "chevron_right" : "chevron_left" }}</mat-icon>
  </button>

  <!-- Header Section -->
  <div class="header-section">
    <!-- Logo -->
    <div class="app-logo">
      <img
        src="logo/logo2.png"
        alt="Chattrix Logo"
        class="logo-image"
        #logoImg
        (error)="logoImg.style.display = 'none'"
      />
      <span
        class="logo-fallback"
        [style.display]="logoImg.style.display === 'none' ? 'flex' : 'none'"
        >C</span
      >
    </div>
    <h2 class="app-name" *ngIf="!isCollapsed">Chattrix</h2>
  </div>

  <!-- Navigation Menu -->
  <div class="navigation-section">
    <mat-nav-list class="nav-list">
      <ng-container *ngFor="let item of navigationItems">
        <mat-list-item
          *ngIf="shouldShowNavItem(item, (hasAdminAccess$ | async) || false)"
          class="nav-item"
          [class.active]="router.url === item.route"
          (click)="onNavItemClick(item)"
          [matTooltip]="isCollapsed ? item.label : ''"
          matTooltipPosition="right"
          matRipple
        >
          <mat-icon matListItemIcon class="nav-icon">{{ item.icon }}</mat-icon>
          <span *ngIf="!isCollapsed" matListItemTitle class="nav-label">{{
            item.label
          }}</span>
          <span
            *ngIf="item.badge && item.badge > 0 && !isCollapsed"
            class="nav-badge"
            matListItemMeta
          >
            {{ item.badge }}
          </span>
        </mat-list-item>
      </ng-container>
    </mat-nav-list>
  </div>

  <!-- Settings Section -->
  <div class="settings-section">
    <mat-divider *ngIf="!isCollapsed"></mat-divider>

    <!-- Dark Mode Toggle -->
    <div
      class="setting-item"
      [matTooltip]="isCollapsed ? 'Toggle Dark Mode' : ''"
      matTooltipPosition="right"
    >
      <mat-icon class="setting-icon">{{
        isDarkMode ? "light_mode" : "dark_mode"
      }}</mat-icon>
      <span *ngIf="!isCollapsed" class="setting-label">Dark Mode</span>
      <mat-slide-toggle
        [checked]="isDarkMode"
        (change)="toggleTheme()"
        class="theme-toggle"
        color="primary"
      ></mat-slide-toggle>
    </div>
  </div>

  <!-- Profile Section - Redesigned -->
  <div class="profile-section">
    <!-- Profile Photo and Info -->
    <div class="profile-info">
      <div
        class="profile-avatar"
        [class.has-image]="userProfile.profilePictureUrl"
      >
        <img
          *ngIf="userProfile.profilePictureUrl"
          [src]="userProfile.profilePictureUrl"
          [alt]="getUserDisplayName(userProfile)"
          class="avatar-image"
          #avatarImg
          (error)="avatarImg.style.display = 'none'"
        />
        <span *ngIf="!userProfile.profilePictureUrl" class="avatar-initials">
          {{ getUserInitials(userProfile) }}
        </span>
      </div>

      <div *ngIf="!isCollapsed" class="profile-details">
        <h3 class="profile-name">{{ getUserDisplayName(userProfile) }}</h3>
        <p *ngIf="shouldShowAdminRole(userProfile)" class="profile-role">
          Admin
        </p>
      </div>
    </div>

    <!-- Logout Button -->
    <button
      mat-icon-button
      class="logout-button"
      (click)="onLogout()"
      [matTooltip]="isCollapsed ? 'Logout' : ''"
      matTooltipPosition="right"
    >
      <mat-icon>logout</mat-icon>
    </button>
  </div>
</div>
