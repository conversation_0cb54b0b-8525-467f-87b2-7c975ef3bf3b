import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';

import { AuthenticationService } from '../../Services/Authentication.service';
import { AuthStateService } from '../../Services/AuthState.service';
import { ForgotPasswordRequest, AuthState, AuthError } from '../../Models';

@Component({
  selector: 'app-forget-password',
  standalone: false,
  templateUrl: './forget-password.component.html',
  styleUrl: './forget-password.component.scss',
})
export class ForgetPasswordComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  forgotPasswordForm: FormGroup;
  logoLoaded = true; // Assume logo loads successfully by default

  // Authentication state
  isLoading = false;
  error: string | null = null;

  constructor(
    private authService: AuthenticationService,
    private authState: AuthStateService,
    private formBuilder: FormBuilder,
    private router: Router,
    private snackBar: MatSnackBar,
  ) {
    this.forgotPasswordForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
    });
  }

  ngOnInit(): void {
    // Subscribe to authentication state for loading and error states
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: AuthState) => {
        this.isLoading = state.isLoading;
        this.error = state.error;

        // Show error messages
        if (state.error) {
          this.showError(state.error);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSendResetLink(): void {
    if (this.forgotPasswordForm.valid) {
      const forgotPasswordData: ForgotPasswordRequest =
        this.forgotPasswordForm.value;

      this.authService.forgotPassword(forgotPasswordData).subscribe({
        next: (response) => {
          if (response.isSuccess) {
            this.showSuccess(
              'Password reset instructions have been sent to your email address.',
            );
            // Navigate to verify reset token page with email parameter
            this.router.navigate(['/auth/verify-reset-token'], {
              queryParams: { email: forgotPasswordData.email },
            });
          }
        },
        error: (error: AuthError) => {
          console.error('Forgot password failed:', error);
        },
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  onImageError(event: any): void {
    this.logoLoaded = false;
    console.log('Logo failed to load, showing fallback');
  }

  private markFormGroupTouched(): void {
    Object.keys(this.forgotPasswordForm.controls).forEach((key) => {
      const control = this.forgotPasswordForm.get(key);
      control?.markAsTouched();
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['success-snackbar'],
    });
  }

  // Getter methods for template
  get emailControl() {
    return this.forgotPasswordForm.get('email');
  }
}
